# Clinical Trial Simulation Module
# This module simulates virtual clinical trials using the QSP model
# and virtual patient populations

library(tidyverse)
library(dplyr)
library(parallel)

# Source required modules
source("code/models/integrated_qsp_model.R")
source("code/simulation/virtual_patient_population.R")

#' Simulate Virtual Clinical Trial
#' 
#' Conducts a complete virtual clinical trial simulation including
#' patient recruitment, treatment assignment, dropout modeling,
#' and outcome assessment
#' 
#' @param n_patients Number of patients to enroll
#' @param treatment_arms Vector of treatment arm names
#' @param study_duration Duration in weeks (default: 24)
#' @param dropout_rate Weekly dropout rate (default: 0.02)
#' @param seed Random seed for reproducibility
#' @return List containing trial results and summary statistics
simulate_virtual_clinical_trial <- function(n_patients = 400,
                                          treatment_arms = c("Placebo", "Pomaglumetad_40mg", 
                                                            "Pomaglumetad_80mg"),
                                          study_duration = 24,
                                          dropout_rate = 0.02,
                                          seed = 789) {
  
  set.seed(seed)
  
  cat("Generating virtual patient population...\n")
  # Generate and stratify virtual population
  virtual_pop <- generate_virtual_population(n_patients * 2, seed = seed)  # Generate extra for screening
  stratified_pop <- stratify_virtual_population(virtual_pop)
  eligible_pop <- apply_inclusion_exclusion_criteria(stratified_pop)
  
  # Select required number of patients
  if (nrow(eligible_pop) < n_patients) {
    warning(paste("Only", nrow(eligible_pop), "eligible patients available, using all"))
    n_patients <- nrow(eligible_pop)
  }
  
  enrolled_patients <- eligible_pop[1:n_patients, ]
  
  cat("Randomizing patients to treatment arms...\n")
  # Randomize to treatment arms
  randomized_patients <- generate_randomization_scheme(
    enrolled_patients, 
    treatment_arms = treatment_arms,
    seed = seed + 1
  )
  
  cat("Simulating patient outcomes...\n")
  # Simulate outcomes for each patient
  trial_results <- simulate_patient_outcomes(
    randomized_patients, 
    study_duration = study_duration,
    dropout_rate = dropout_rate,
    seed = seed + 2
  )
  
  cat("Analyzing trial results...\n")
  # Analyze trial results
  trial_analysis <- analyze_trial_results(trial_results)
  
  # Compile final results
  final_results <- list(
    study_design = list(
      n_patients = n_patients,
      treatment_arms = treatment_arms,
      study_duration = study_duration,
      dropout_rate = dropout_rate
    ),
    patient_population = randomized_patients,
    individual_results = trial_results,
    statistical_analysis = trial_analysis,
    simulation_seed = seed
  )
  
  return(final_results)
}

#' Simulate Patient Outcomes
#' 
#' Simulates individual patient outcomes using the integrated QSP model
#' 
#' @param randomized_patients Randomized patient population
#' @param study_duration Study duration in weeks
#' @param dropout_rate Weekly dropout rate
#' @param seed Random seed
#' @return Data frame with patient outcomes
simulate_patient_outcomes <- function(randomized_patients, 
                                     study_duration = 24,
                                     dropout_rate = 0.02,
                                     seed = 123) {
  
  set.seed(seed)
  
  # Define dose mapping
  dose_mapping <- list(
    "Placebo" = 0,
    "Pomaglumetad_40mg" = 40,
    "Pomaglumetad_80mg" = 80,
    "Active_Control" = 0  # Gabapentin or similar (not modeled in QSP)
  )
  
  # Initialize results list
  all_patient_results <- list()
  
  # Simulate each patient (can be parallelized)
  for (i in 1:nrow(randomized_patients)) {
    patient <- randomized_patients[i, ]
    
    # Determine if patient drops out and when
    dropout_time <- simulate_dropout(study_duration, dropout_rate, patient)
    
    # Get patient-specific parameters
    patient_params <- get_patient_specific_parameters(patient)
    
    # Determine dose
    dose <- dose_mapping[[patient$treatment_arm]]
    
    # Simulate QSP model if not placebo/control
    if (dose > 0) {
      # Run integrated QSP simulation
      qsp_results <- simulate_integrated_qsp(
        dose = dose,
        interval = 12,  # BID dosing
        n_doses = min(dropout_time * 2 * 7, study_duration * 2 * 7),  # Twice daily for weeks
        glucose_level = patient$fasting_glucose,
        baseline_weight = patient$weight_kg,
        parameters = patient_params
      )
      
      # Extract key endpoints at study timepoints
      timepoints <- c(0, 4, 8, 12, 16, 20, 24) * 7 * 24  # Convert weeks to hours
      timepoints <- timepoints[timepoints <= max(qsp_results$time)]
      
      endpoint_data <- extract_endpoint_data(qsp_results, timepoints, patient)
      
    } else {
      # Simulate placebo/control response
      endpoint_data <- simulate_placebo_response(patient, study_duration, dropout_time)
    }
    
    # Add patient metadata
    endpoint_data$patient_id <- patient$patient_id
    endpoint_data$treatment_arm <- patient$treatment_arm
    endpoint_data$dropout_time <- dropout_time
    endpoint_data$completed_study <- dropout_time >= study_duration
    
    all_patient_results[[i]] <- endpoint_data
    
    # Progress indicator
    if (i %% 50 == 0) {
      cat(paste("Completed", i, "of", nrow(randomized_patients), "patients\n"))
    }
  }
  
  # Combine all results
  combined_results <- do.call(rbind, all_patient_results)
  
  return(combined_results)
}

#' Get Patient-Specific Parameters
#' 
#' Adjusts model parameters based on individual patient characteristics
#' 
#' @param patient Single patient data row
#' @return Modified parameter list
get_patient_specific_parameters <- function(patient) {
  
  # Start with default parameters
  params <- get_integrated_parameters()
  
  # Adjust based on genetic factors
  params$R_mGluR2_total <- params$R_mGluR2_total * patient$mglur2_expression
  params$R_mGluR3_total <- params$R_mGluR3_total * patient$mglur3_expression
  
  # Adjust drug metabolism
  params$CL_prod <- params$CL_prod * patient$cyp_activity
  params$CL_active <- params$CL_active * patient$cyp_activity
  
  # Adjust antioxidant capacity
  params$k_sod2_basal <- params$k_sod2_basal * patient$sod2_activity
  params$GSH_max <- params$GSH_max * patient$gsh_capacity
  
  # Adjust inflammatory response
  params$k_tnf_basal <- params$k_tnf_basal * patient$tnf_alpha_baseline
  params$k_il1_basal <- params$k_il1_basal * patient$il1_beta_baseline
  
  # Adjust based on diabetes type
  if (patient$diabetes_type == "Type1") {
    # Type 1 patients may have different inflammatory profiles
    params$k_tnf_basal <- params$k_tnf_basal * 0.8
    params$k_il1_basal <- params$k_il1_basal * 0.8
  }
  
  # Adjust based on comorbidities
  if (patient$nephropathy) {
    # Reduced drug clearance in nephropathy
    params$CL_prod <- params$CL_prod * 0.7
    params$CL_active <- params$CL_active * 0.7
  }
  
  if (patient$hypertension) {
    # Altered vascular function may affect drug distribution
    params$Q_prod <- params$Q_prod * 0.9
    params$Q_active <- params$Q_active * 0.9
  }
  
  return(params)
}

#' Simulate Dropout
#' 
#' Simulates patient dropout based on baseline characteristics and adverse events
#' 
#' @param study_duration Study duration in weeks
#' @param base_dropout_rate Base weekly dropout rate
#' @param patient Patient characteristics
#' @return Dropout time in weeks (or study_duration if no dropout)
simulate_dropout <- function(study_duration, base_dropout_rate, patient) {
  
  # Adjust dropout rate based on patient characteristics
  adjusted_rate <- base_dropout_rate
  
  # Higher dropout in older patients
  if (patient$age > 65) adjusted_rate <- adjusted_rate * 1.3
  
  # Higher dropout with more comorbidities
  adjusted_rate <- adjusted_rate * (1 + 0.2 * patient$comorbidity_count)
  
  # Lower dropout in patients with severe neuropathy (more motivated)
  if (patient$neuropathy_severity == "Severe") adjusted_rate <- adjusted_rate * 0.8
  
  # Simulate time to dropout (exponential distribution)
  time_to_dropout <- rexp(1, rate = adjusted_rate)
  
  # Convert to weeks and cap at study duration
  dropout_week <- min(time_to_dropout, study_duration)
  
  return(dropout_week)
}

#' Extract Endpoint Data
#' 
#' Extracts key efficacy and safety endpoints from QSP simulation results
#' 
#' @param qsp_results QSP simulation results
#' @param timepoints Time points to extract (in hours)
#' @param patient Patient characteristics
#' @return Data frame with endpoint data
extract_endpoint_data <- function(qsp_results, timepoints, patient) {
  
  # Find closest time points in simulation
  endpoint_indices <- sapply(timepoints, function(t) {
    which.min(abs(qsp_results$time - t))
  })
  
  endpoint_data <- qsp_results[endpoint_indices, ] %>%
    mutate(
      study_week = timepoints / (7 * 24),
      
      # Primary endpoint: composite neuropathy score (calculate if not present)
      primary_endpoint = if("neuropathy_score" %in% colnames(.)) {
        neuropathy_score
      } else {
        (20 - IENFD) * 2 + (50 - NCV) * 1 + ROS * 5 + (TNF_alpha - 1) * 2
      },
      
      # Secondary endpoints
      weight_change_kg = Body_weight - patient$weight_kg,
      weight_change_percent = ((Body_weight - patient$weight_kg) / patient$weight_kg) * 100,
      
      # Pain scores (simulated based on neuropathy improvement)
      vas_pain_score = pmax(0, pmin(10, 6 - (20 - IENFD) * 0.3 + rnorm(length(IENFD), 0, 0.5))),
      
      # Quality of life (simulated)
      qol_score = pmax(0, pmin(100, 50 + (IENFD - 10) * 2 - (ROS - 1) * 10 + rnorm(length(IENFD), 0, 5))),
      
      # Safety endpoints
      adverse_events = simulate_adverse_events(receptor_occupancy, study_week),
      
      # Biomarkers
      glutamate_level = Glutamate,
      oxidative_stress = ROS,
      inflammation_score = (TNF_alpha + IL1_beta) / 2,
      mitochondrial_function = Mito_function
    ) %>%
    select(study_week, primary_endpoint, weight_change_kg, weight_change_percent,
           vas_pain_score, qol_score, adverse_events, IENFD, NCV, 
           glutamate_level, oxidative_stress, inflammation_score, 
           mitochondrial_function, receptor_occupancy)
  
  return(endpoint_data)
}

#' Analyze Trial Results
#'
#' Performs statistical analysis of virtual clinical trial results
#'
#' @param trial_results Combined trial results data frame
#' @return List with statistical analysis results
analyze_trial_results <- function(trial_results) {

  # Primary endpoint analysis (change from baseline at week 24)
  primary_analysis <- trial_results %>%
    filter(study_week %in% c(0, 24)) %>%
    group_by(patient_id, treatment_arm) %>%
    summarise(
      baseline_score = first(primary_endpoint),
      endpoint_score = last(primary_endpoint),
      change_from_baseline = last(primary_endpoint) - first(primary_endpoint),
      .groups = "drop"
    )

  # ANCOVA model for primary endpoint
  if (length(unique(primary_analysis$treatment_arm)) > 1) {
    primary_model <- lm(change_from_baseline ~ treatment_arm + baseline_score,
                       data = primary_analysis)
    primary_results <- summary(primary_model)
  } else {
    primary_results <- NULL
  }

  # Secondary endpoint analyses
  secondary_analyses <- list()

  # Weight loss analysis
  weight_analysis <- trial_results %>%
    filter(study_week == 24) %>%
    group_by(treatment_arm) %>%
    summarise(
      n = n(),
      mean_weight_change = mean(weight_change_kg, na.rm = TRUE),
      sd_weight_change = sd(weight_change_kg, na.rm = TRUE),
      prop_5pct_loss = mean(weight_change_percent <= -5, na.rm = TRUE),
      prop_10pct_loss = mean(weight_change_percent <= -10, na.rm = TRUE),
      .groups = "drop"
    )

  secondary_analyses$weight_loss <- weight_analysis

  # Pain analysis
  pain_analysis <- trial_results %>%
    filter(study_week %in% c(0, 24)) %>%
    group_by(patient_id, treatment_arm) %>%
    summarise(
      pain_change = last(vas_pain_score) - first(vas_pain_score),
      .groups = "drop"
    ) %>%
    group_by(treatment_arm) %>%
    summarise(
      n = n(),
      mean_pain_change = mean(pain_change, na.rm = TRUE),
      sd_pain_change = sd(pain_change, na.rm = TRUE),
      .groups = "drop"
    )

  secondary_analyses$pain_reduction <- pain_analysis

  # Safety analysis
  safety_analysis <- trial_results %>%
    group_by(patient_id, treatment_arm) %>%
    summarise(
      total_adverse_events = sum(adverse_events, na.rm = TRUE),
      .groups = "drop"
    ) %>%
    group_by(treatment_arm) %>%
    summarise(
      n = n(),
      mean_adverse_events = mean(total_adverse_events, na.rm = TRUE),
      patients_with_ae = sum(total_adverse_events > 0),
      ae_rate = patients_with_ae / n,
      .groups = "drop"
    )

  # Biomarker analysis
  biomarker_analysis <- trial_results %>%
    filter(study_week %in% c(0, 24)) %>%
    group_by(patient_id, treatment_arm) %>%
    summarise(
      glutamate_change = last(glutamate_level) - first(glutamate_level),
      oxidative_stress_change = last(oxidative_stress) - first(oxidative_stress),
      inflammation_change = last(inflammation_score) - first(inflammation_score),
      .groups = "drop"
    ) %>%
    group_by(treatment_arm) %>%
    summarise(
      n = n(),
      mean_glutamate_change = mean(glutamate_change, na.rm = TRUE),
      mean_oxidative_stress_change = mean(oxidative_stress_change, na.rm = TRUE),
      mean_inflammation_change = mean(inflammation_change, na.rm = TRUE),
      .groups = "drop"
    )

  # Compile results
  analysis_results <- list(
    primary_endpoint = list(
      model = primary_model,
      results = primary_results,
      summary_stats = primary_analysis %>%
        group_by(treatment_arm) %>%
        summarise(
          n = n(),
          mean_change = mean(change_from_baseline, na.rm = TRUE),
          sd_change = sd(change_from_baseline, na.rm = TRUE),
          .groups = "drop"
        )
    ),
    secondary_endpoints = secondary_analyses,
    safety = safety_analysis,
    biomarkers = biomarker_analysis,
    completion_rates = trial_results %>%
      group_by(treatment_arm) %>%
      summarise(
        n_enrolled = length(unique(patient_id)),
        n_completed = sum(study_week == 24, na.rm = TRUE),
        completion_rate = n_completed / n_enrolled,
        .groups = "drop"
      )
  )

  return(analysis_results)
}

#' Simulate Adverse Events
#' 
#' Simulates adverse events based on receptor occupancy and time
#' 
#' @param receptor_occupancy Receptor occupancy levels
#' @param study_week Study week
#' @return Number of adverse events
simulate_adverse_events <- function(receptor_occupancy, study_week) {
  
  # Base adverse event rate
  base_rate <- 0.1
  
  # Increase with receptor occupancy (dose-related)
  occupancy_effect <- receptor_occupancy / 100 * 0.3
  
  # Time-dependent effects (tolerance development)
  time_effect <- pmax(0, 1 - study_week * 0.02)
  
  # Total adverse event probability
  ae_prob <- base_rate + occupancy_effect * time_effect
  
  # Simulate adverse events (Poisson)
  adverse_events <- rpois(length(ae_prob), ae_prob)
  
  return(adverse_events)
}

#' Simulate Placebo Response
#' 
#' Simulates placebo/control group response
#' 
#' @param patient Patient characteristics
#' @param study_duration Study duration in weeks
#' @param dropout_time Dropout time in weeks
#' @return Data frame with placebo response data
simulate_placebo_response <- function(patient, study_duration, dropout_time) {
  
  # Time points
  study_weeks <- c(0, 4, 8, 12, 16, 20, 24)
  study_weeks <- study_weeks[study_weeks <= dropout_time]
  
  # Placebo effect (small improvement that wanes over time)
  placebo_effect <- exp(-study_weeks / 12) * 0.1  # 10% initial improvement, decaying
  
  # Natural disease progression (slight worsening)
  disease_progression <- study_weeks * 0.02
  
  # Baseline neuropathy score
  baseline_score <- (20 - patient$ienfd) * 2 + (50 - patient$ncv) * 1 + 
                   patient$crp * 5 + 10  # Approximate baseline score
  
  endpoint_data <- data.frame(
    study_week = study_weeks,
    primary_endpoint = baseline_score * (1 - placebo_effect + disease_progression),
    weight_change_kg = rnorm(length(study_weeks), 0, 0.5),  # Random fluctuation
    weight_change_percent = rnorm(length(study_weeks), 0, 0.6),
    vas_pain_score = pmax(0, pmin(10, 6 + rnorm(length(study_weeks), 0, 0.8))),
    qol_score = pmax(0, pmin(100, 50 + rnorm(length(study_weeks), 0, 8))),
    adverse_events = rpois(length(study_weeks), 0.05),  # Low AE rate for placebo
    IENFD = patient$ienfd * (1 - disease_progression * 0.01),
    NCV = patient$ncv * (1 - disease_progression * 0.01),
    glutamate_level = 5 + rnorm(length(study_weeks), 0, 0.5),
    oxidative_stress = 1 + rnorm(length(study_weeks), 0, 0.2),
    inflammation_score = patient$crp + rnorm(length(study_weeks), 0, 0.3),
    mitochondrial_function = 100 - disease_progression * 2,
    receptor_occupancy = 0  # No drug
  )
  
  return(endpoint_data)
}

