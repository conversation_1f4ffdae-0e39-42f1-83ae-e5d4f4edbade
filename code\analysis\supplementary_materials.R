# Supplementary Materials Creation
# This module creates detailed statistical results tables, model parameters,
# data summary statistics, and supporting documentation

library(tidyverse)
library(dplyr)
library(knitr)
library(DT)
library(formattable)

#' Generate All Supplementary Materials
#' 
#' Creates comprehensive supplementary materials for the research report
#' 
#' @param virtual_trial_results Results from virtual clinical trial
#' @param qsp_model_results QSP model simulation results
#' @param output_dir Directory to save materials
#' @return List of generated materials
generate_supplementary_materials <- function(virtual_trial_results, qsp_model_results, output_dir = "docs/supplementary/") {
  
  # Create output directory
  dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)
  
  cat("Generating statistical results tables...\n")
  statistical_tables <- generate_statistical_tables(virtual_trial_results, output_dir)
  
  cat("Generating model parameter tables...\n")
  parameter_tables <- generate_parameter_tables(qsp_model_results, output_dir)
  
  cat("Generating data summary statistics...\n")
  summary_statistics <- generate_summary_statistics(virtual_trial_results, output_dir)
  
  cat("Generating biomarker documentation...\n")
  biomarker_documentation <- generate_biomarker_documentation(virtual_trial_results, output_dir)
  
  all_materials <- list(
    statistical_tables = statistical_tables,
    parameter_tables = parameter_tables,
    summary_statistics = summary_statistics,
    biomarker_documentation = biomarker_documentation
  )
  
  cat("All supplementary materials generated successfully!\n")
  return(all_materials)
}

#' Generate Statistical Results Tables
generate_statistical_tables <- function(virtual_trial_results, output_dir) {
  
  statistical_results <- virtual_trial_results$statistical_results
  
  # Table S1: Primary Endpoint Analysis Results
  primary_results <- statistical_results$primary_endpoint$summary_stats
  
  table_s1 <- primary_results %>%
    mutate(
      `Mean Change (95% CI)` = paste0(
        round(mean_change, 2), " (", 
        round(mean_change - 1.96 * se_change, 2), ", ",
        round(mean_change + 1.96 * se_change, 2), ")"
      ),
      `Effect Size (Cohen's d)` = round(cohens_d, 3),
      `Sample Size` = n
    ) %>%
    select(`Treatment Arm` = treatment_arm, 
           `Sample Size`,
           `Mean Change (95% CI)`,
           `Effect Size (Cohen's d)`) %>%
    mutate(`Treatment Arm` = case_when(
      `Treatment Arm` == "Placebo" ~ "Placebo",
      `Treatment Arm` == "Pomaglumetad_40mg" ~ "Pomaglumetad 40mg BID",
      `Treatment Arm` == "Pomaglumetad_80mg" ~ "Pomaglumetad 80mg BID",
      TRUE ~ `Treatment Arm`
    ))
  
  # Save as CSV and HTML
  write.csv(table_s1, file.path(output_dir, "table_s1_primary_endpoint.csv"), row.names = FALSE)
  
  table_s1_html <- table_s1 %>%
    formattable(
      list(
        `Effect Size (Cohen's d)` = color_tile("white", "lightblue"),
        `Sample Size` = color_bar("lightgray")
      )
    )
  
  # Table S2: Secondary Endpoint Results
  weight_results <- statistical_results$secondary_endpoints$weight_loss$categorical_summary
  pain_results <- statistical_results$secondary_endpoints$pain$responder_analysis
  
  table_s2 <- data.frame(
    `Treatment Arm` = weight_results$treatment_arm,
    `Mean Weight Change (kg)` = round(weight_results$mean_weight_change_kg, 2),
    `Proportion ≥5% Weight Loss` = paste0(round(weight_results$prop_5pct_loss * 100, 1), "%"),
    `Proportion ≥10% Weight Loss` = paste0(round(weight_results$prop_10pct_loss * 100, 1), "%"),
    check.names = FALSE
  ) %>%
    mutate(`Treatment Arm` = case_when(
      `Treatment Arm` == "Placebo" ~ "Placebo",
      `Treatment Arm` == "Pomaglumetad_40mg" ~ "Pomaglumetad 40mg BID",
      `Treatment Arm` == "Pomaglumetad_80mg" ~ "Pomaglumetad 80mg BID",
      TRUE ~ `Treatment Arm`
    ))
  
  write.csv(table_s2, file.path(output_dir, "table_s2_weight_loss.csv"), row.names = FALSE)
  
  # Table S3: Safety Summary
  if (!is.null(virtual_trial_results$safety_results)) {
    safety_results <- virtual_trial_results$safety_results
    
    table_s3 <- safety_results %>%
      mutate(
        `Patients with AE (%)` = paste0(patients_with_ae, " (", round(ae_rate * 100, 1), "%)"),
        `Mean AE per Patient` = round(mean_adverse_events, 2)
      ) %>%
      select(`Treatment Arm` = treatment_arm,
             `Sample Size` = n,
             `Patients with AE (%)`,
             `Mean AE per Patient`) %>%
      mutate(`Treatment Arm` = case_when(
        `Treatment Arm` == "Placebo" ~ "Placebo",
        `Treatment Arm` == "Pomaglumetad_40mg" ~ "Pomaglumetad 40mg BID",
        `Treatment Arm` == "Pomaglumetad_80mg" ~ "Pomaglumetad 80mg BID",
        TRUE ~ `Treatment Arm`
      ))
    
    write.csv(table_s3, file.path(output_dir, "table_s3_safety_summary.csv"), row.names = FALSE)
  }
  
  statistical_tables <- list(
    primary_endpoint = table_s1,
    weight_loss = table_s2,
    safety = if(exists("table_s3")) table_s3 else NULL
  )
  
  return(statistical_tables)
}

#' Generate Model Parameter Tables
generate_parameter_tables <- function(qsp_model_results, output_dir) {
  
  # Get default parameters for documentation
  disease_params <- get_default_neuropathy_parameters()
  pk_params <- get_default_pk_parameters()
  pd_params <- get_default_pd_parameters()
  
  # Table S4: Disease Model Parameters
  disease_param_df <- data.frame(
    Parameter = names(disease_params),
    Value = unlist(disease_params),
    Units = c(
      "mg/dL", "mg/dL", "μM/h", "μM/h", "μM", "1/h", "μM", 
      "AU/h", "AU/h", "AU/h", "1/h", "1/h", "U/mg", 
      "μg/mL/h per (mg/dL)²", "1/h", "pg/mL/h", "pg/mL/h per AU", 
      "pg/mL/h per μg/mL", "1/h", "pg/mL/h", "pg/mL/h per pg/mL", 
      "pg/mL/h per AU", "1/h", "1/h per AU", "dimensionless", "1/h",
      "U/mg/h", "U/mg/h", "AU", "1/h", "μM/h", "μM", "1/h per AU", "μM",
      "1/h per combined factors", "dimensionless", "1/h", "fibers/mm",
      "1/h per combined factors", "1/h", "m/s"
    ),
    Description = c(
      "Half-saturation for glucose effects", "Threshold for AGE formation",
      "Glutamate release rate", "Maximum glutamate uptake rate", 
      "Half-saturation for glutamate uptake", "Glutamate decay rate",
      "Half-saturation for glutamate-induced ROS", "Basal ROS production",
      "Glucose-induced ROS production", "Glutamate-induced ROS production",
      "ROS scavenging rate", "ROS decay rate", "Half-saturation for SOD2 scavenging",
      "AGE formation rate", "AGE clearance rate", "Basal TNF-α production",
      "ROS-induced TNF-α production", "AGE-induced TNF-α production",
      "TNF-α clearance rate", "Basal IL-1β production", 
      "TNF-α-induced IL-1β production", "ROS-induced IL-1β production",
      "IL-1β clearance rate", "Mitochondrial damage rate",
      "Glutamate contribution to mito damage", "Mitochondrial repair rate",
      "Basal SOD2 production", "ROS-induced SOD2 production",
      "Half-saturation for SOD2 induction", "SOD2 degradation rate",
      "GSH synthesis rate", "Maximum GSH concentration",
      "GSH consumption rate", "Half-saturation for GSH consumption",
      "IENFD damage rate", "Glutamate contribution to IENFD damage",
      "IENFD repair rate", "Maximum IENFD", "NCV damage rate",
      "NCV repair rate", "Maximum NCV"
    ),
    stringsAsFactors = FALSE
  )
  
  write.csv(disease_param_df, file.path(output_dir, "table_s4_disease_parameters.csv"), row.names = FALSE)
  
  # Table S5: Pharmacokinetic Parameters
  pk_param_df <- data.frame(
    Parameter = names(pk_params),
    Value = unlist(pk_params),
    Units = c(
      "1/h", "dimensionless", "L/70kg", "L/70kg", "L/70kg", "L/70kg",
      "L/h", "L/h", "1/h", "dimensionless", "1/h", "1/h", "1/h", "1/h",
      "L/h", "L/h", "L/h", "1/M/h", "1/h", "M", "1/M/h", "1/h", "M", "g/mol"
    ),
    Description = c(
      "Absorption rate constant", "PEPT1 transporter enhancement factor",
      "Central compartment volume", "Peripheral compartment volume",
      "CNS compartment volume", "DRG compartment volume",
      "Prodrug inter-compartmental clearance", "Active metabolite inter-compartmental clearance",
      "Conversion rate constant", "Conversion efficiency", "CNS influx rate constant",
      "CNS efflux rate constant", "DRG influx rate constant", "DRG efflux rate constant",
      "Prodrug clearance", "Active metabolite clearance", "CNS clearance",
      "mGluR2 association rate constant", "mGluR2 dissociation rate constant",
      "Total mGluR2 receptors", "mGluR3 association rate constant",
      "mGluR3 dissociation rate constant", "Total mGluR3 receptors",
      "Molecular weight of LY404039"
    ),
    stringsAsFactors = FALSE
  )
  
  write.csv(pk_param_df, file.path(output_dir, "table_s5_pk_parameters.csv"), row.names = FALSE)
  
  parameter_tables <- list(
    disease_parameters = disease_param_df,
    pk_parameters = pk_param_df
  )
  
  return(parameter_tables)
}

#' Generate Summary Statistics
generate_summary_statistics <- function(virtual_trial_results, output_dir) {
  
  patient_data <- virtual_trial_results$randomized_patients
  treatment_data <- virtual_trial_results$treatment_results
  
  # Table S6: Baseline Characteristics Summary
  baseline_summary <- patient_data %>%
    group_by(treatment_arm) %>%
    summarise(
      N = n(),
      `Age (years)` = paste0(round(mean(age), 1), " ± ", round(sd(age), 1)),
      `Female (%)` = paste0(sum(gender == "Female"), " (", round(sum(gender == "Female")/n()*100, 1), "%)"),
      `Type 2 Diabetes (%)` = paste0(sum(diabetes_type == "Type2"), " (", round(sum(diabetes_type == "Type2")/n()*100, 1), "%)"),
      `BMI (kg/m²)` = paste0(round(mean(bmi), 1), " ± ", round(sd(bmi), 1)),
      `Diabetes Duration (years)` = paste0(round(mean(diabetes_duration), 1), " ± ", round(sd(diabetes_duration), 1)),
      `HbA1c (%)` = paste0(round(mean(hba1c), 1), " ± ", round(sd(hba1c), 1)),
      `MNSI Score` = paste0(round(mean(mnsi_score), 1), " ± ", round(sd(mnsi_score), 1)),
      `IENFD (fibers/mm)` = paste0(round(mean(ienfd), 1), " ± ", round(sd(ienfd), 1)),
      `NCV (m/s)` = paste0(round(mean(ncv), 1), " ± ", round(sd(ncv), 1)),
      .groups = "drop"
    ) %>%
    mutate(treatment_arm = case_when(
      treatment_arm == "Placebo" ~ "Placebo",
      treatment_arm == "Pomaglumetad_40mg" ~ "Pomaglumetad 40mg BID",
      treatment_arm == "Pomaglumetad_80mg" ~ "Pomaglumetad 80mg BID",
      TRUE ~ treatment_arm
    )) %>%
    rename(`Treatment Arm` = treatment_arm)
  
  write.csv(baseline_summary, file.path(output_dir, "table_s6_baseline_characteristics.csv"), row.names = FALSE)
  
  # Table S7: Study Completion and Dropout Analysis
  completion_summary <- treatment_data %>%
    group_by(treatment_arm) %>%
    summarise(
      `Enrolled (N)` = length(unique(patient_id)),
      `Completed Week 24 (N)` = sum(study_week == 24, na.rm = TRUE),
      `Completion Rate (%)` = round(sum(study_week == 24, na.rm = TRUE) / length(unique(patient_id)) * 100, 1),
      `Mean Study Duration (weeks)` = round(mean(pmax(study_week), na.rm = TRUE), 1),
      .groups = "drop"
    ) %>%
    mutate(treatment_arm = case_when(
      treatment_arm == "Placebo" ~ "Placebo",
      treatment_arm == "Pomaglumetad_40mg" ~ "Pomaglumetad 40mg BID",
      treatment_arm == "Pomaglumetad_80mg" ~ "Pomaglumetad 80mg BID",
      TRUE ~ treatment_arm
    )) %>%
    rename(`Treatment Arm` = treatment_arm)
  
  write.csv(completion_summary, file.path(output_dir, "table_s7_study_completion.csv"), row.names = FALSE)
  
  summary_stats <- list(
    baseline_characteristics = baseline_summary,
    study_completion = completion_summary
  )
  
  return(summary_stats)
}

#' Generate Biomarker Documentation
generate_biomarker_documentation <- function(virtual_trial_results, output_dir) {
  
  # Table S8: Biomarker Definitions and Rationale
  biomarker_definitions <- data.frame(
    `Biomarker Category` = c(
      rep("Mechanistic", 8),
      rep("Clinical Efficacy", 6),
      rep("Safety Monitoring", 4)
    ),
    `Biomarker Name` = c(
      "mGluR2/3 Receptor Occupancy", "Glutamate Level", "Glutamate Transporter Activity",
      "Glutamine Synthetase Activity", "SIRT1 Activity", "PGC-1α Level", 
      "Mitochondrial Function", "Oxidative Stress (ROS)",
      "IENFD", "NCV", "Composite Neuropathy Score", "Pain Score (VAS)",
      "Quality of Life Score", "Weight Change",
      "Hepatic Function (ALT/AST)", "Renal Function (eGFR)", 
      "Cardiovascular Parameters", "Psychiatric Risk Score"
    ),
    `Units/Scale` = c(
      "%", "μM", "% of baseline", "% of baseline", "% of maximum", "% of maximum",
      "% of normal", "Arbitrary units",
      "fibers/mm", "m/s", "Points", "0-10 scale", "0-100 scale", "kg or %",
      "U/L", "mL/min/1.73m²", "mmHg, bpm", "0-10 scale"
    ),
    `Clinical Rationale` = c(
      "Direct measure of target engagement", "Reflects glutamate homeostasis",
      "Indicates glutamate recycling capacity", "Measures glutamate clearance efficiency",
      "Key neuroprotective pathway activation", "Mitochondrial biogenesis regulator",
      "Overall mitochondrial health", "Oxidative damage marker",
      "Structural nerve damage assessment", "Functional nerve assessment",
      "Integrated neuropathy severity", "Subjective pain assessment",
      "Patient-reported outcome", "Metabolic efficacy measure",
      "Hepatotoxicity monitoring", "Nephrotoxicity monitoring",
      "Cardiovascular safety", "Psychiatric adverse effects"
    ),
    stringsAsFactors = FALSE,
    check.names = FALSE
  )
  
  write.csv(biomarker_definitions, file.path(output_dir, "table_s8_biomarker_definitions.csv"), row.names = FALSE)
  
  biomarker_docs <- list(
    definitions = biomarker_definitions
  )
  
  return(biomarker_docs)
}
