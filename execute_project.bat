@echo off
echo ===============================================
echo QSP Pomaglumetad Methionil Project Execution
echo ===============================================
echo.

echo Checking R installation...
"C:\Program Files\R\R-4.2.2\bin\Rscript.exe" --version
if %errorlevel% neq 0 (
    echo ERROR: R is not installed or not in PATH
    echo Please install R from: https://cran.r-project.org/bin/windows/base/
    echo Then run this script again.
    pause
    exit /b 1
)

echo.
echo Changing to project directory...
cd /d "%~dp0"

echo.
echo Step 1: Installing required R packages...
echo This may take several minutes on first run...
"C:\Program Files\R\R-4.2.2\bin\Rscript.exe" -e "source('setup.R')"

echo.
echo Step 2: Running main analysis...
echo This will execute the complete QSP analysis pipeline...
"C:\Program Files\R\R-4.2.2\bin\Rscript.exe" -e "source('main_analysis.R')"

echo.
echo ===============================================
echo Analysis completed! 
echo Check the following directories for results:
echo - figures/     : Generated plots and visualizations
echo - results/     : Statistical analysis outputs  
echo - docs/reports/: Research report and documentation
echo ===============================================
pause
