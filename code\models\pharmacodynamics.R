# Pharmacodynamic Model for Pomaglumetad Methionil
# This module models the drug's mechanism of action through mGluR2/3 activation
# and downstream neuroprotective and metabolic effects

library(deSolve)
library(tidyverse)

#' Pomaglumetad Methionil Pharmacodynamic Model
#' 
#' Models the multifaceted mechanism of action:
#' - mGluR2/3 receptor activation and downstream signaling
#' - Neuroprotective pathways (SIRT1-PGC-1α-TFAM axis)
#' - Glutamate recycling enhancement
#' - Anti-inflammatory effects
#' - Weight loss mechanisms
#'
#' @param time Time vector
#' @param state State variables vector
#' @param parameters Model parameters list
#' @param receptor_occupancy mGluR2/3 receptor occupancy (%)
#' @return List of derivatives for ODE system

pomaglumetad_pd_model <- function(time, state, parameters, receptor_occupancy) {
  with(as.list(c(state, parameters)), {
    
    # State variables:
    # cAMP: Cyclic adenosine monophosphate level (μM)
    # SIRT1: SIRT1 protein activity (% of maximum)
    # PGC1a: PGC-1α protein level (% of maximum)
    # TFAM: TFAM protein level (% of maximum)
    # Mito_biogenesis: Mitochondrial biogenesis rate (% of maximum)
    # Glu_transporter: Glutamate transporter expression (% of maximum)
    # Gln_synthetase: Glutamine synthetase activity (% of maximum)
    # SOD2_drug: Drug-induced SOD2 activity (U/mg protein)
    # GSH_drug: Drug-induced GSH synthesis (μM/h)
    # GFAP: Glial fibrillary acidic protein expression (% of baseline)
    # Body_weight: Body weight (kg)
    # Appetite: Appetite score (arbitrary units)
    # Metabolic_rate: Metabolic rate (% of baseline)
    # Insulin_sensitivity: Insulin sensitivity index (% of baseline)
    
    # Receptor activation effects
    receptor_activation <- receptor_occupancy / 100
    
    # mGluR2/3 receptor signaling
    # G-protein coupling (Gi/Go) and adenylyl cyclase inhibition
    adenylyl_cyclase_inhibition <- max_AC_inhibition * receptor_activation / 
                                  (receptor_activation + EC50_AC_inhibition)
    
    # cAMP dynamics
    camp_synthesis <- k_camp_synth * (1 - adenylyl_cyclase_inhibition)
    camp_degradation <- k_camp_deg * cAMP
    
    # SIRT1 pathway activation (enhanced by reduced cAMP)
    sirt1_activation <- k_sirt1_basal + k_sirt1_receptor * receptor_activation +
                       k_sirt1_camp * (1 / (1 + cAMP / IC50_camp_sirt1))
    sirt1_degradation <- k_sirt1_deg * SIRT1
    
    # PGC-1α activation (downstream of SIRT1)
    pgc1a_activation <- k_pgc1a_basal + k_pgc1a_sirt1 * SIRT1 / (SIRT1 + EC50_sirt1_pgc1a)
    pgc1a_degradation <- k_pgc1a_deg * PGC1a
    
    # TFAM activation (downstream of PGC-1α)
    tfam_activation <- k_tfam_basal + k_tfam_pgc1a * PGC1a / (PGC1a + EC50_pgc1a_tfam)
    tfam_degradation <- k_tfam_deg * TFAM
    
    # Mitochondrial biogenesis (downstream of TFAM)
    mito_biogenesis_rate <- k_mito_biog_basal + k_mito_biog_tfam * TFAM / (TFAM + EC50_tfam_mito)
    mito_biogenesis_decay <- k_mito_biog_decay * Mito_biogenesis
    
    # Glutamate homeostasis enhancement
    # Glutamate transporter upregulation
    glu_trans_induction <- k_glu_trans_basal + k_glu_trans_receptor * receptor_activation
    glu_trans_degradation <- k_glu_trans_deg * Glu_transporter
    
    # Glutamine synthetase enhancement
    gln_synt_induction <- k_gln_synt_basal + k_gln_synt_receptor * receptor_activation
    gln_synt_degradation <- k_gln_synt_deg * Gln_synthetase
    
    # Antioxidant defense enhancement
    sod2_drug_induction <- k_sod2_drug_basal + k_sod2_drug_receptor * receptor_activation +
                          k_sod2_drug_sirt1 * SIRT1 / (SIRT1 + EC50_sirt1_sod2)
    sod2_drug_degradation <- k_sod2_drug_deg * SOD2_drug
    
    # GSH synthesis enhancement
    gsh_drug_induction <- k_gsh_drug_basal + k_gsh_drug_receptor * receptor_activation +
                         k_gsh_drug_pgc1a * PGC1a / (PGC1a + EC50_pgc1a_gsh)
    gsh_drug_degradation <- k_gsh_drug_deg * GSH_drug
    
    # Anti-inflammatory effects
    # GFAP suppression (glial activation marker)
    gfap_suppression <- k_gfap_suppress * receptor_activation / (receptor_activation + EC50_gfap_suppress)
    gfap_basal_expression <- k_gfap_basal * (1 - gfap_suppression)
    gfap_degradation <- k_gfap_deg * GFAP
    
    # Weight loss mechanisms
    # Central appetite regulation
    appetite_suppression <- max_appetite_suppress * receptor_activation / 
                           (receptor_activation + EC50_appetite_suppress)
    appetite_recovery <- k_appetite_recovery * (appetite_baseline - Appetite)
    
    # Metabolic rate enhancement
    metabolic_enhancement <- max_metabolic_enhance * receptor_activation /
                            (receptor_activation + EC50_metabolic_enhance)
    metabolic_rate_change <- k_metabolic_change * metabolic_enhancement
    metabolic_rate_decay <- k_metabolic_decay * (Metabolic_rate - 100)
    
    # Insulin sensitivity improvement
    insulin_improvement <- max_insulin_improve * receptor_activation /
                          (receptor_activation + EC50_insulin_improve)
    insulin_sens_change <- k_insulin_change * insulin_improvement
    insulin_sens_decay <- k_insulin_decay * (Insulin_sensitivity - 100)
    
    # Body weight dynamics
    # Weight change based on appetite and metabolic rate
    caloric_balance <- appetite_effect * (Appetite / appetite_baseline) - 
                      metabolic_effect * (Metabolic_rate / 100)
    weight_change_rate <- k_weight_change * caloric_balance
    
    # Differential equations
    dcAMP <- camp_synthesis - camp_degradation
    
    dSIRT1 <- sirt1_activation - sirt1_degradation
    
    dPGC1a <- pgc1a_activation - pgc1a_degradation
    
    dTFAM <- tfam_activation - tfam_degradation
    
    dMito_biogenesis <- mito_biogenesis_rate - mito_biogenesis_decay
    
    dGlu_transporter <- glu_trans_induction - glu_trans_degradation
    
    dGln_synthetase <- gln_synt_induction - gln_synt_degradation
    
    dSOD2_drug <- sod2_drug_induction - sod2_drug_degradation
    
    dGSH_drug <- gsh_drug_induction - gsh_drug_degradation
    
    dGFAP <- gfap_basal_expression - gfap_degradation
    
    dBody_weight <- weight_change_rate
    
    dAppetite <- -appetite_suppression + appetite_recovery
    
    dMetabolic_rate <- metabolic_rate_change - metabolic_rate_decay
    
    dInsulin_sensitivity <- insulin_sens_change - insulin_sens_decay
    
    # Return derivatives
    list(c(dcAMP, dSIRT1, dPGC1a, dTFAM, dMito_biogenesis, dGlu_transporter,
           dGln_synthetase, dSOD2_drug, dGSH_drug, dGFAP, dBody_weight,
           dAppetite, dMetabolic_rate, dInsulin_sensitivity))
  })
}

#' Default pharmacodynamic parameters
get_default_pd_parameters <- function() {
  list(
    # cAMP signaling parameters
    k_camp_synth = 10,         # cAMP synthesis rate (μM/h)
    k_camp_deg = 2,            # cAMP degradation rate (1/h)
    max_AC_inhibition = 0.8,   # Maximum adenylyl cyclase inhibition
    EC50_AC_inhibition = 0.3,  # EC50 for AC inhibition
    
    # SIRT1 pathway parameters
    k_sirt1_basal = 5,         # Basal SIRT1 activity (% max/h)
    k_sirt1_receptor = 20,     # Receptor-mediated SIRT1 activation
    k_sirt1_camp = 15,         # cAMP-mediated SIRT1 activation
    IC50_camp_sirt1 = 5,       # IC50 for cAMP inhibition of SIRT1 (μM)
    k_sirt1_deg = 0.1,         # SIRT1 degradation rate (1/h)
    
    # PGC-1α parameters
    k_pgc1a_basal = 2,         # Basal PGC-1α level (% max/h)
    k_pgc1a_sirt1 = 10,        # SIRT1-mediated PGC-1α activation
    EC50_sirt1_pgc1a = 50,     # EC50 for SIRT1 effect on PGC-1α
    k_pgc1a_deg = 0.08,        # PGC-1α degradation rate (1/h)
    
    # TFAM parameters
    k_tfam_basal = 1,          # Basal TFAM level (% max/h)
    k_tfam_pgc1a = 8,          # PGC-1α-mediated TFAM activation
    EC50_pgc1a_tfam = 40,      # EC50 for PGC-1α effect on TFAM
    k_tfam_deg = 0.06,         # TFAM degradation rate (1/h)
    
    # Mitochondrial biogenesis parameters
    k_mito_biog_basal = 1,     # Basal mitochondrial biogenesis (% max/h)
    k_mito_biog_tfam = 5,      # TFAM-mediated biogenesis
    EC50_tfam_mito = 30,       # EC50 for TFAM effect on biogenesis
    k_mito_biog_decay = 0.05,  # Biogenesis decay rate (1/h)
    
    # Glutamate homeostasis parameters
    k_glu_trans_basal = 2,     # Basal glutamate transporter expression
    k_glu_trans_receptor = 8,  # Receptor-mediated transporter induction
    k_glu_trans_deg = 0.1,     # Transporter degradation rate (1/h)
    
    k_gln_synt_basal = 1.5,    # Basal glutamine synthetase activity
    k_gln_synt_receptor = 6,   # Receptor-mediated synthetase induction
    k_gln_synt_deg = 0.08,     # Synthetase degradation rate (1/h)
    
    # Antioxidant defense parameters
    k_sod2_drug_basal = 1,     # Basal drug-induced SOD2 (U/mg/h)
    k_sod2_drug_receptor = 5,  # Receptor-mediated SOD2 induction
    k_sod2_drug_sirt1 = 3,     # SIRT1-mediated SOD2 induction
    EC50_sirt1_sod2 = 40,      # EC50 for SIRT1 effect on SOD2
    k_sod2_drug_deg = 0.1,     # SOD2 degradation rate (1/h)
    
    k_gsh_drug_basal = 2,      # Basal drug-induced GSH synthesis (μM/h)
    k_gsh_drug_receptor = 8,   # Receptor-mediated GSH induction
    k_gsh_drug_pgc1a = 4,      # PGC-1α-mediated GSH induction
    EC50_pgc1a_gsh = 35,       # EC50 for PGC-1α effect on GSH
    k_gsh_drug_deg = 0.2,      # GSH degradation rate (1/h)
    
    # Anti-inflammatory parameters
    k_gfap_basal = 5,          # Basal GFAP expression (% baseline/h)
    k_gfap_suppress = 0.6,     # Maximum GFAP suppression
    EC50_gfap_suppress = 0.4,  # EC50 for GFAP suppression
    k_gfap_deg = 0.1,          # GFAP degradation rate (1/h)
    
    # Weight loss parameters
    max_appetite_suppress = 30, # Maximum appetite suppression (%)
    EC50_appetite_suppress = 0.5, # EC50 for appetite suppression
    appetite_baseline = 100,    # Baseline appetite score
    k_appetite_recovery = 0.02, # Appetite recovery rate (1/h)
    
    max_metabolic_enhance = 15, # Maximum metabolic rate enhancement (%)
    EC50_metabolic_enhance = 0.4, # EC50 for metabolic enhancement
    k_metabolic_change = 0.5,   # Metabolic rate change rate (1/h)
    k_metabolic_decay = 0.01,   # Metabolic rate decay rate (1/h)
    
    max_insulin_improve = 25,   # Maximum insulin sensitivity improvement (%)
    EC50_insulin_improve = 0.6, # EC50 for insulin improvement
    k_insulin_change = 0.3,     # Insulin sensitivity change rate (1/h)
    k_insulin_decay = 0.005,    # Insulin sensitivity decay rate (1/h)
    
    # Body weight dynamics (reduced to realistic levels)
    appetite_effect = 0.002,    # Appetite effect on weight (kg/day per %) - reduced 10x
    metabolic_effect = 0.0015,  # Metabolic rate effect on weight (kg/day per %) - reduced 10x
    k_weight_change = 0.01      # Weight change rate constant (1/h) - reduced 10x
  )
}

#' Initial conditions for pharmacodynamic model
get_pd_initial_conditions <- function() {
  c(
    cAMP = 5,                  # Baseline cAMP level (μM)
    SIRT1 = 50,                # Baseline SIRT1 activity (% max)
    PGC1a = 40,                # Baseline PGC-1α level (% max)
    TFAM = 30,                 # Baseline TFAM level (% max)
    Mito_biogenesis = 20,      # Baseline mitochondrial biogenesis (% max)
    Glu_transporter = 100,     # Baseline glutamate transporter (% max)
    Gln_synthetase = 100,      # Baseline glutamine synthetase (% max)
    SOD2_drug = 0,             # Drug-induced SOD2 (starts at 0)
    GSH_drug = 0,              # Drug-induced GSH synthesis (starts at 0)
    GFAP = 100,                # Baseline GFAP expression (% baseline)
    Body_weight = 85,          # Baseline body weight (kg)
    Appetite = 100,            # Baseline appetite score
    Metabolic_rate = 100,      # Baseline metabolic rate (% baseline)
    Insulin_sensitivity = 100  # Baseline insulin sensitivity (% baseline)
  )
}

#' Simulate pharmacodynamic response
#' 
#' @param receptor_occupancy_profile Vector of receptor occupancy over time (%)
#' @param times Time vector corresponding to occupancy profile
#' @param parameters PD parameters (optional)
#' @return Data frame with PD simulation results
simulate_pd_response <- function(receptor_occupancy_profile,
                                times,
                                parameters = NULL) {
  
  if (is.null(parameters)) {
    parameters <- get_default_pd_parameters()
  }
  
  # Initial conditions
  initial_state <- get_pd_initial_conditions()
  
  # Create interpolation function for receptor occupancy
  occupancy_func <- approxfun(times, receptor_occupancy_profile, rule = 2)
  
  # Modified PD model function with time-varying occupancy
  pd_model_with_occupancy <- function(time, state, parameters) {
    current_occupancy <- occupancy_func(time)
    pomaglumetad_pd_model(time, state, parameters, current_occupancy)
  }
  
  # Solve ODE system
  solution <- ode(y = initial_state,
                  times = times,
                  func = pd_model_with_occupancy,
                  parms = parameters,
                  method = "lsoda")
  
  # Convert to data frame
  result <- as.data.frame(solution)
  result$receptor_occupancy <- receptor_occupancy_profile
  
  return(result)
}

