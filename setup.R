# QSP Pomaglumetad Methionil Project Setup
# This script installs all required packages and sets up the environment

# CRAN packages for QSP modeling and analysis
required_packages <- c(
  # Core data manipulation and analysis
  "tidyverse",
  "dplyr",
  "ggplot2",
  "readr",
  "tidyr",
  
  # Statistical analysis
  "lme4",           # Mixed-effects models
  "nlme",           # Nonlinear mixed-effects models
  "survival",       # Survival analysis
  "rstan",          # Bayesian analysis
  "brms",           # Bayesian regression models
  "MCMCglmm",       # MCMC methods
  
  # Pharmacokinetic/Pharmacodynamic modeling
  "RxODE",          # ODE-based PK/PD modeling
  "mrgsolve",       # Simulation from ODE-based models
  "NONMEM2R",       # Interface to NONMEM
  "PKPDmodels",     # PK/PD model library
  
  # Machine learning
  "randomForest",   # Random forest
  "caret",          # Classification and regression training
  "e1071",          # SVM and other ML methods
  "glmnet",         # Regularized regression
  "xgboost",        # Gradient boosting
  
  # Visualization
  "ggplot2",        # Grammar of graphics
  "plotly",         # Interactive plots
  "corrplot",       # Correlation plots
  "pheatmap",       # Heatmaps
  "VennDiagram",    # Venn diagrams
  "gridExtra",      # Grid arrangements
  
  # Simulation and sampling
  "mvtnorm",        # Multivariate normal distribution
  "truncnorm",      # Truncated normal distribution
  "MASS",           # Various statistical functions
  "boot",           # Bootstrap methods
  
  # Reporting and documentation
  "knitr",          # Dynamic report generation
  "rmarkdown",      # R Markdown
  "DT",             # Interactive tables
  "formattable",    # Formatted tables
  
  # Specialized packages
  "dose",           # Dose-response analysis
  "drc",            # Dose-response curves
  "mixtools",       # Mixture models
  "cluster",        # Cluster analysis
  "factoextra",     # Factor analysis and PCA
  
  # Parallel computing
  "parallel",       # Parallel processing
  "foreach",        # Foreach loops
  "doParallel",     # Parallel backend
  
  # Data validation
  "validate",       # Data validation rules
  "VIM",            # Visualization of missing values
  
  # Network analysis (for pathway modeling)
  "igraph",         # Graph analysis
  "network",        # Network objects
  
  # Time series analysis
  "forecast",       # Time series forecasting
  "tseries",        # Time series analysis
  
  # Optimization
  "optimx",         # Optimization methods
  "nloptr",         # Nonlinear optimization
  
  # Bioinformatics (for pathway analysis)
  "limma",          # Linear models for microarray data
  "qvalue"          # Q-value estimation
)

# Function to install packages if not already installed
install_if_missing <- function(packages) {
  new_packages <- packages[!(packages %in% installed.packages()[,"Package"])]
  if(length(new_packages)) {
    cat("Installing missing packages:", paste(new_packages, collapse = ", "), "\n")
    install.packages(new_packages, dependencies = TRUE)
  } else {
    cat("All required packages are already installed.\n")
  }
}

# Install CRAN packages
cat("Setting up QSP Pomaglumetad Methionil Project Environment...\n")
cat("Installing CRAN packages...\n")
install_if_missing(required_packages)

# Bioconductor packages (if needed)
bioc_packages <- c(
  "limma",
  "qvalue"
)

# Install Bioconductor packages
if (!requireNamespace("BiocManager", quietly = TRUE)) {
  install.packages("BiocManager")
}

for (pkg in bioc_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    cat("Installing Bioconductor package:", pkg, "\n")
    BiocManager::install(pkg)
  }
}

# Load core packages for immediate use
library(tidyverse)
library(ggplot2)
library(dplyr)

# Set global options
options(
  stringsAsFactors = FALSE,
  digits = 4,
  scipen = 6
)

# Create .Rprofile for consistent settings
rprofile_content <- '
# QSP Project .Rprofile
options(
  stringsAsFactors = FALSE,
  digits = 4,
  scipen = 6,
  repos = c(CRAN = "https://cran.rstudio.com/")
)

# Set default ggplot2 theme
if (requireNamespace("ggplot2", quietly = TRUE)) {
  ggplot2::theme_set(ggplot2::theme_minimal())
}

cat("QSP Pomaglumetad Methionil Project Environment Loaded\\n")
'

writeLines(rprofile_content, ".Rprofile")

cat("Setup complete! Project environment is ready.\n")
cat("Restart R session to activate .Rprofile settings.\n")
