# Mathematical Expressions for QSP Model

## Overview

This document provides detailed mathematical expressions for all components of the Quantitative Systems Pharmacology (QSP) model for pomaglumetad methionil in diabetic neuropathy.

## 1. Disease Pathophysiology Module

### 1.1 Glutamatergic Dysfunction

The glutamate dynamics in dorsal root ganglia (DRG) neurons are described by:

```
dGlu/dt = k_glu_release × (Glucose/(Glucose + KM_glucose)) - k_glu_uptake × (Glu/(Glu + KM_glu_uptake)) - k_glu_decay × Glu
```

Where:
- `Glu` = extracellular glutamate concentration (μM)
- `k_glu_release` = glutamate release rate constant (μM/h)
- `k_glu_uptake` = maximum glutamate uptake rate (μM/h)
- `KM_glu_uptake` = half-saturation constant for glutamate uptake (μM)
- `k_glu_decay` = glutamate decay rate constant (1/h)

### 1.2 Oxidative Stress and ROS Generation

Reactive oxygen species (ROS) dynamics:

```
dROS/dt = k_ros_basal + k_ros_glucose × (Glucose/(Glucose + KM_glucose)) + k_ros_glu × (Glu/(Glu + KM_glu_ros)) - k_ros_scav × ROS × (SOD2/(SOD2 + KM_sod2)) - k_ros_decay × ROS
```

Where:
- `ROS` = reactive oxygen species level (arbitrary units)
- `k_ros_basal` = basal ROS production rate (AU/h)
- `k_ros_glucose` = glucose-induced ROS production (AU/h)
- `k_ros_glu` = glutamate-induced ROS production (AU/h)
- `k_ros_scav` = ROS scavenging rate (1/h)
- `SOD2` = superoxide dismutase 2 activity (U/mg protein)

### 1.3 Advanced Glycation End Products (AGE)

AGE formation and clearance:

```
dAGE/dt = k_age_form × Glucose × max(0, Glucose - glucose_threshold) - k_age_clear × AGE
```

Where:
- `AGE` = advanced glycation end products concentration (μg/mL)
- `k_age_form` = AGE formation rate constant (μg/mL/h per (mg/dL)²)
- `glucose_threshold` = threshold glucose level for AGE formation (mg/dL)
- `k_age_clear` = AGE clearance rate constant (1/h)

### 1.4 Inflammatory Cascade

TNF-α dynamics:

```
dTNF_α/dt = k_tnf_basal + k_tnf_ros × ROS + k_tnf_age × AGE - k_tnf_clear × TNF_α
```

IL-1β dynamics:

```
dIL1_β/dt = k_il1_basal + k_il1_tnf × TNF_α + k_il1_ros × ROS - k_il1_clear × IL1_β
```

### 1.5 Nerve Structure and Function

Intraepidermal nerve fiber density (IENFD):

```
dIENFD/dt = -k_ienfd_damage × (ROS + TNF_α + IL1_β + k_ienfd_glu × Glu) × IENFD + k_ienfd_repair × (IENFD_max - IENFD)
```

Nerve conduction velocity (NCV):

```
dNCV/dt = -k_ncv_damage × (ROS + TNF_α + IL1_β + AGE) × NCV + k_ncv_repair × (NCV_max - NCV)
```

## 2. Pharmacokinetic Module

### 2.1 Absorption and Distribution

Gut compartment (oral dosing):

```
dA_gut/dt = -ka_eff × A_gut
```

Where `ka_eff = ka × (1 + PEPT1_expression)`

Central compartment (prodrug):

```
dA_central_prod/dt = ka_eff × A_gut - Q_prod × (C_central_prod - C_periph_prod) - k_conv × A_central_prod × conversion_efficiency - CL_prod × C_central_prod
```

### 2.2 Prodrug Conversion

The conversion of pomaglumetad methionil to active LY404039:

```
Conversion_rate = k_conv × A_prodrug × conversion_efficiency
```

Where `conversion_efficiency ≈ 0.7` (70% conversion)

### 2.3 Receptor Binding Kinetics

mGluR2 binding:

```
dA_bound_mGluR2/dt = kon_mGluR2 × C_DRG_free × R_mGluR2_free - koff_mGluR2 × A_bound_mGluR2
```

mGluR3 binding:

```
dA_bound_mGluR3/dt = kon_mGluR3 × C_DRG_free × R_mGluR3_free - koff_mGluR3 × A_bound_mGluR3
```

Receptor occupancy:

```
Occupancy_mGluR2 = A_bound_mGluR2 / R_mGluR2_total × 100%
Occupancy_mGluR3 = A_bound_mGluR3 / R_mGluR3_total × 100%
```

## 3. Pharmacodynamic Module

### 3.1 mGluR2/3 Receptor Signaling

Adenylyl cyclase inhibition:

```
AC_inhibition = max_AC_inhibition × (receptor_occupancy/(receptor_occupancy + EC50_AC_inhibition))
```

cAMP dynamics:

```
dcAMP/dt = k_camp_synth × (1 - AC_inhibition) - k_camp_deg × cAMP
```

### 3.2 SIRT1-PGC-1α-TFAM Neuroprotective Pathway

SIRT1 activation:

```
dSIRT1/dt = k_sirt1_basal + k_sirt1_receptor × receptor_occupancy + k_sirt1_camp × (1/(1 + cAMP/IC50_camp_sirt1)) - k_sirt1_deg × SIRT1
```

PGC-1α activation:

```
dPGC1α/dt = k_pgc1a_basal + k_pgc1a_sirt1 × (SIRT1/(SIRT1 + EC50_sirt1_pgc1a)) - k_pgc1a_deg × PGC1α
```

TFAM activation:

```
dTFAM/dt = k_tfam_basal + k_tfam_pgc1a × (PGC1α/(PGC1α + EC50_pgc1a_tfam)) - k_tfam_deg × TFAM
```

### 3.3 Mitochondrial Biogenesis

```
dMito_biogenesis/dt = k_mito_biog_basal + k_mito_biog_tfam × (TFAM/(TFAM + EC50_tfam_mito)) - k_mito_biog_decay × Mito_biogenesis
```

### 3.4 Glutamate Homeostasis Enhancement

Glutamate transporter upregulation:

```
dGlu_transporter/dt = k_glu_trans_basal + k_glu_trans_receptor × receptor_occupancy - k_glu_trans_deg × Glu_transporter
```

Glutamine synthetase enhancement:

```
dGln_synthetase/dt = k_gln_synt_basal + k_gln_synt_receptor × receptor_occupancy - k_gln_synt_deg × Gln_synthetase
```

### 3.5 Weight Loss Mechanisms

Appetite suppression:

```
dAppetite/dt = -max_appetite_suppress × (receptor_occupancy/(receptor_occupancy + EC50_appetite_suppress)) + k_appetite_recovery × (appetite_baseline - Appetite)
```

Metabolic rate enhancement:

```
dMetabolic_rate/dt = k_metabolic_change × max_metabolic_enhance × (receptor_occupancy/(receptor_occupancy + EC50_metabolic_enhance)) - k_metabolic_decay × (Metabolic_rate - 100)
```

Body weight dynamics:

```
dBody_weight/dt = k_weight_change × [appetite_effect × (Appetite/appetite_baseline) - metabolic_effect × (Metabolic_rate/100)]
```

## 4. Integrated Model Interactions

### 4.1 Drug Effects on Disease Progression

Enhanced glutamate uptake effect:

```
Enhanced_uptake_factor = 1 + (Glu_transporter - 100)/100
Modified_glutamate_uptake = k_glu_uptake × Enhanced_uptake_factor × (Glu/(Glu + KM_glu_uptake))
```

Enhanced antioxidant defense:

```
Total_SOD2 = SOD2_endogenous + SOD2_drug
Total_GSH = GSH_endogenous + GSH_drug
Enhanced_ROS_scavenging = k_ros_scav × ROS × (Total_SOD2/(Total_SOD2 + KM_sod2))
```

Mitochondrial function enhancement:

```
dMito_function/dt = -k_mito_damage × (ROS + k_mito_glu × Glu) + k_mito_repair × Mito_function + k_mito_drug_enhance × (Mito_biogenesis/100)
```

### 4.2 Composite Endpoints

Composite neuropathy score:

```
Neuropathy_score = (20 - IENFD) × 2 + (50 - NCV) × 1 + ROS × 5 + (TNF_α - 1) × 2
```

Treatment response probability:

```
Response_probability = 1/(1 + exp(-[(Target_engagement - 50)/20 + (Therapeutic_benefit - 10)/15 - Safety_composite/30]))
```

## 5. Statistical Models

### 5.1 Primary Endpoint Analysis (ANCOVA)

```
Change_from_baseline = β₀ + β₁ × Treatment_arm + β₂ × Baseline_score + β₃ × Diabetes_type + β₄ × Age + β₅ × BMI + ε
```

### 5.2 Mixed-Effects Model

```
Y_ij = β₀ + β₁ × Treatment_i + β₂ × Time_j + β₃ × (Treatment_i × Time_j) + b₀ᵢ + b₁ᵢ × Time_j + ε_ij
```

Where:
- `Y_ij` = outcome for patient i at time j
- `b₀ᵢ, b₁ᵢ` = random intercept and slope for patient i
- `ε_ij` = residual error

### 5.3 Logistic Regression (Responder Analysis)

```
logit(P(Response)) = β₀ + β₁ × Treatment_arm + β₂ × Baseline_score + β₃ × Covariates
```

### 5.4 Survival Analysis (Time to Response)

Hazard function:

```
h(t|x) = h₀(t) × exp(β₁ × Treatment_arm + β₂ × Covariates)
```

## 6. Biomarker Relationships

### 6.1 Mechanistic Biomarkers

Target engagement score:

```
Target_engagement = 0.4 × Receptor_occupancy + 0.3 × Glutamate_recycling_efficiency + 0.3 × SIRT1_pathway_score
```

### 6.2 Efficacy Biomarkers

Therapeutic benefit score:

```
Therapeutic_benefit = 0.5 × Nerve_function_improvement + 0.3 × Metabolic_improvement + 0.2 × Inflammatory_improvement
```

### 6.3 Safety Biomarkers

Safety composite score:

```
Safety_composite = Σ(Weight_i × Safety_flag_i)
```

Where weights are assigned based on clinical severity.

## 7. Model Validation Metrics

### 7.1 Goodness of Fit

Coefficient of determination:

```
R² = 1 - (SS_res/SS_tot)
```

### 7.2 Predictive Performance

Mean absolute prediction error:

```
MAPE = (1/n) × Σ|Y_observed - Y_predicted|/Y_observed × 100%
```

### 7.3 Sensitivity Analysis

Parameter sensitivity:

```
S_i = (∂Y/∂θᵢ) × (θᵢ/Y)
```

Where `θᵢ` is parameter i and `Y` is the model output.

---

**Note**: All equations are implemented in the R code modules with appropriate numerical methods for solving the differential equation systems. Parameter values and their sources are documented in the supplementary materials.
