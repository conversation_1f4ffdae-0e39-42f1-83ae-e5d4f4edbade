# QSP Pomaglumetad Methionil Project - Execution Guide

## 🎯 Project Overview

This is a comprehensive **Quantitative Systems Pharmacology (QSP)** research project that investigates the therapeutic potential of **Pomaglumetad Methionil** in treating **Diabetic Neuropathy**, with particular focus on its weight loss benefits.

## 📊 What This Project Does

### Core Components:
1. **Disease Pathophysiology Model**: Simulates diabetic neuropathy progression
2. **Pharmacokinetic Model**: Models drug absorption, distribution, metabolism, and elimination
3. **Pharmacodynamic Model**: Simulates mGluR2/3 receptor activation and downstream effects
4. **Virtual Patient Population**: Generates diverse diabetic patient phenotypes
5. **Virtual Clinical Trial**: Simulates clinical trial scenarios
6. **Biomarker Framework**: Integrates mechanistic and clinical biomarkers
7. **Statistical Analysis**: Comprehensive efficacy and safety analysis

### Expected Outputs:
- Virtual patient populations (Type 1 & Type 2 diabetes)
- Clinical trial simulation results
- Biomarker validation results
- Statistical analysis reports
- Comprehensive visualizations
- Research report documentation

## 🚀 Quick Start Guide

### Prerequisites
- **R** (≥ 4.0.0) - Download from [CRAN](https://cran.r-project.org/bin/windows/base/)
- **RStudio** (recommended) - Download from [RStudio](https://www.rstudio.com/products/rstudio/download/)
- **4-8 GB RAM** available
- **2-4 GB disk space** for packages and results

### Installation Steps

1. **Install R:**
   ```
   Download from: https://cran.r-project.org/bin/windows/base/
   Install with default settings
   ```

2. **Install RStudio (Optional but Recommended):**
   ```
   Download from: https://www.rstudio.com/products/rstudio/download/
   Install with default settings
   ```

3. **Verify Installation:**
   ```
   Open Command Prompt and run: Rscript --version
   Should display R version information
   ```

## ⚡ Execution Methods

### Method 1: Using the Batch File (Easiest)
```batch
# Double-click the file:
execute_project.bat
```

### Method 2: Using R Console
```r
# Set working directory to project folder
setwd("c:/Users/<USER>/OneDrive - aiimsbhubaneswar.edu.in/QSP_Pome_DM_Neuropathy")

# Install required packages (first time only)
source("setup.R")

# Run complete analysis
source("main_analysis.R")
```

### Method 3: Using RStudio
1. Open RStudio
2. Open the project file: `QSP_Pome_DM_Neuropathy.Rproj`
3. Run in Console:
   ```r
   source("setup.R")      # Install packages
   source("main_analysis.R")  # Run analysis
   ```

### Method 4: Using VS Code with R Extension
1. Install R Extension for VS Code
2. Open project folder in VS Code
3. Open integrated terminal
4. Run:
   ```r
   Rscript -e "source('setup.R')"
   Rscript -e "source('main_analysis.R')"
   ```

## 📋 Execution Phases

The analysis runs in 7 phases:

1. **QSP Model Demonstration** (2-3 minutes)
   - Disease pathophysiology simulation
   - Pharmacokinetic modeling
   - Integrated QSP model execution

2. **Virtual Patient Population** (3-5 minutes)
   - Generate 1000 virtual patients
   - Apply stratification and inclusion/exclusion criteria
   - Population characterization

3. **Virtual Clinical Trial** (5-10 minutes)
   - Conduct virtual clinical trial (n=400)
   - Randomization and treatment allocation
   - Efficacy and safety endpoint calculation

4. **Biomarker Analysis** (2-3 minutes)
   - Integrate mechanistic biomarkers
   - Calculate biomarker responses
   - Biomarker validation

5. **Statistical Analysis** (3-5 minutes)
   - Primary and secondary endpoint analysis
   - Bayesian and frequentist statistics
   - Effect size calculations

6. **Figure Generation** (5-8 minutes)
   - Generate all visualizations
   - Create publication-ready figures
   - Export to figures/ directory

7. **Supplementary Materials** (2-3 minutes)
   - Create supplementary documentation
   - Generate additional analyses
   - Export to docs/supplementary/

**Total Expected Runtime: 20-40 minutes**

## 📁 Output Structure

After execution, you'll find:

```
QSP_Pome_DM_Neuropathy/
├── figures/
│   ├── exploratory/          # Exploratory data analysis plots
│   ├── results/              # Main results visualizations  
│   └── supplementary/        # Supplementary figures
├── results/
│   ├── model_outputs/        # QSP model simulation results
│   ├── statistics/           # Statistical analysis outputs
│   └── tables/               # Summary tables
├── data/
│   ├── processed/            # Processed datasets
│   └── simulated/            # Simulated patient data
├── docs/
│   ├── reports/              # Main research report
│   └── supplementary/        # Supplementary materials
└── QSP_Analysis_Workspace.RData  # Complete R workspace
```

## 🔍 Key Results to Check

1. **Main Research Report:**
   `docs/reports/QSP_Pomaglumetad_Research_Report.md`

2. **Key Figures:**
   - `figures/results/qsp_model_overview.png`
   - `figures/results/virtual_trial_results.png`
   - `figures/results/biomarker_analysis.png`

3. **Statistical Results:**
   - `results/statistics/primary_endpoint_analysis.csv`
   - `results/statistics/secondary_endpoints.csv`

4. **Model Outputs:**
   - `results/model_outputs/qsp_simulation_results.csv`
   - `results/model_outputs/virtual_population.csv`

## 🛠️ Troubleshooting

### Common Issues:

1. **R Package Installation Errors:**
   ```r
   # Update R packages
   update.packages(ask = FALSE)
   
   # Install specific packages manually
   install.packages(c("tidyverse", "deSolve", "ggplot2"))
   ```

2. **Memory Issues:**
   - Close other applications
   - Increase virtual memory
   - Run analysis in smaller chunks

3. **Missing Dependencies:**
   ```r
   # Reinstall setup
   source("setup.R")
   ```

4. **File Path Issues:**
   - Ensure no special characters in folder path
   - Use forward slashes in R: `/` instead of `\`

### Getting Help:

- Check `README.md` for detailed documentation
- Review error messages in R console
- Check individual R script files for specific issues
- Verify all required files are present using `project_helper.py`

## 🎉 Success Indicators

You'll know the analysis completed successfully when you see:

```
=== ALL ANALYSES COMPLETED SUCCESSFULLY ===
Total runtime: X.X minutes

✓ QSP model implemented and validated
✓ Virtual patient population generated (n=1000)
✓ Virtual clinical trial conducted (n=400)  
✓ Biomarker framework integrated
✓ Statistical analysis completed
✓ Figures and visualizations generated
✓ Supplementary materials created
✓ Research report available

🎉 QSP Analysis Protocol Implementation Complete! 🎉
```

## 📞 Support

For technical questions about the analysis, refer to:
- Project documentation in `docs/`
- Individual R script comments
- Research protocol in `Protocol.md`

This project implements a state-of-the-art QSP modeling approach for drug development and clinical research in diabetic neuropathy.
