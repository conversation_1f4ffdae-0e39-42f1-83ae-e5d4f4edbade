# Comprehensive Statistical Analysis Framework
# This module implements statistical methods for analyzing QSP model results
# and virtual clinical trial data

library(tidyverse)
library(dplyr)
library(lme4)
library(nlme)
library(survival)
library(rstan)
library(brms)
library(randomForest)
library(caret)
library(glmnet)

#' Primary Endpoint Analysis
#' 
#' Analyzes the primary efficacy endpoint using ANCOVA and mixed-effects models
#' 
#' @param trial_data Virtual clinical trial results
#' @param endpoint_name Name of primary endpoint variable
#' @param baseline_vars Vector of baseline covariates
#' @return List with analysis results
analyze_primary_endpoint <- function(trial_data, 
                                   endpoint_name = "composite_neuropathy_score",
                                   baseline_vars = c("baseline_score", "diabetes_type", 
                                                    "neuropathy_severity", "age", "bmi")) {
  
  # Prepare data for analysis
  analysis_data <- trial_data %>%
    filter(study_week %in% c(0, 24)) %>%
    group_by(patient_id) %>%
    mutate(
      baseline_score = first(get(endpoint_name)),
      endpoint_score = last(get(endpoint_name)),
      change_from_baseline = endpoint_score - baseline_score
    ) %>%
    filter(study_week == 24) %>%  # Keep only endpoint data
    ungroup()
  
  # ANCOVA model (primary analysis)
  ancova_formula <- as.formula(paste("change_from_baseline ~ treatment_arm +", 
                                    paste(baseline_vars, collapse = " + ")))
  
  ancova_model <- lm(ancova_formula, data = analysis_data)
  ancova_results <- summary(ancova_model)
  
  # Mixed-effects model (secondary analysis accounting for repeated measures)
  mixed_data <- trial_data %>%
    filter(study_week >= 0)
  
  mixed_formula <- as.formula(paste(endpoint_name, "~ treatment_arm * study_week +", 
                                   paste(baseline_vars[-1], collapse = " + "), 
                                   "+ (1 + study_week | patient_id)"))
  
  mixed_model <- lmer(mixed_formula, data = mixed_data)
  mixed_results <- summary(mixed_model)
  
  # Effect size calculations
  effect_sizes <- analysis_data %>%
    group_by(treatment_arm) %>%
    summarise(
      n = n(),
      mean_change = mean(change_from_baseline, na.rm = TRUE),
      sd_change = sd(change_from_baseline, na.rm = TRUE),
      se_change = sd_change / sqrt(n),
      .groups = "drop"
    ) %>%
    mutate(
      # Cohen's d vs placebo (assuming first group is placebo)
      cohens_d = ifelse(row_number() == 1, 0, 
                       (mean_change - first(mean_change)) / 
                       sqrt((sd_change^2 + first(sd_change)^2) / 2))
    )
  
  # Responder analysis (≥30% improvement)
  responder_threshold <- -0.3  # 30% improvement (negative because lower scores are better)
  responder_analysis <- analysis_data %>%
    mutate(
      percent_change = change_from_baseline / baseline_score,
      responder = percent_change <= responder_threshold
    ) %>%
    group_by(treatment_arm) %>%
    summarise(
      n = n(),
      n_responders = sum(responder, na.rm = TRUE),
      response_rate = n_responders / n,
      .groups = "drop"
    )
  
  # Logistic regression for responder analysis
  responder_model <- glm(responder ~ treatment_arm + baseline_score + diabetes_type, 
                        data = analysis_data %>% 
                          mutate(percent_change = change_from_baseline / baseline_score,
                                responder = percent_change <= responder_threshold),
                        family = binomial)
  
  # Compile results
  primary_results <- list(
    ancova = list(model = ancova_model, results = ancova_results),
    mixed_effects = list(model = mixed_model, results = mixed_results),
    effect_sizes = effect_sizes,
    responder_analysis = list(
      summary = responder_analysis,
      model = responder_model,
      results = summary(responder_model)
    ),
    sample_sizes = analysis_data %>% count(treatment_arm)
  )
  
  return(primary_results)
}

#' Secondary Endpoint Analysis
#' 
#' Analyzes secondary efficacy endpoints
#' 
#' @param trial_data Virtual clinical trial results
#' @return List with secondary endpoint analyses
analyze_secondary_endpoints <- function(trial_data) {
  
  # Weight loss analysis
  weight_analysis <- analyze_weight_loss(trial_data)
  
  # Pain analysis
  pain_analysis <- analyze_pain_outcomes(trial_data)
  
  # Nerve function analysis
  nerve_function_analysis <- analyze_nerve_function(trial_data)
  
  # Quality of life analysis
  qol_analysis <- analyze_quality_of_life(trial_data)
  
  # Time-to-event analysis (time to response)
  tte_analysis <- analyze_time_to_response(trial_data)
  
  secondary_results <- list(
    weight_loss = weight_analysis,
    pain = pain_analysis,
    nerve_function = nerve_function_analysis,
    quality_of_life = qol_analysis,
    time_to_event = tte_analysis
  )
  
  return(secondary_results)
}

#' Weight Loss Analysis
#' 
#' Analyzes weight loss outcomes
#' 
#' @param trial_data Trial data
#' @return Weight loss analysis results
analyze_weight_loss <- function(trial_data) {
  
  # Continuous weight change analysis
  weight_data <- trial_data %>%
    filter(study_week == 24) %>%
    select(patient_id, treatment_arm, weight_change_kg, weight_change_percent)
  
  # ANOVA for weight change
  weight_model <- lm(weight_change_kg ~ treatment_arm, data = weight_data)
  
  # Categorical weight loss analysis
  weight_categories <- weight_data %>%
    mutate(
      weight_loss_5pct = weight_change_percent <= -5,
      weight_loss_10pct = weight_change_percent <= -10
    ) %>%
    group_by(treatment_arm) %>%
    summarise(
      n = n(),
      mean_weight_change_kg = mean(weight_change_kg, na.rm = TRUE),
      mean_weight_change_pct = mean(weight_change_percent, na.rm = TRUE),
      prop_5pct_loss = mean(weight_loss_5pct, na.rm = TRUE),
      prop_10pct_loss = mean(weight_loss_10pct, na.rm = TRUE),
      .groups = "drop"
    )
  
  # Logistic regression for categorical outcomes
  weight_5pct_model <- glm(weight_loss_5pct ~ treatment_arm, 
                          data = weight_data %>% 
                            mutate(weight_loss_5pct = weight_change_percent <= -5),
                          family = binomial)
  
  return(list(
    continuous_model = weight_model,
    categorical_summary = weight_categories,
    logistic_model = weight_5pct_model
  ))
}

#' Pain Analysis
#' 
#' Analyzes pain outcome measures
#' 
#' @param trial_data Trial data
#' @return Pain analysis results
analyze_pain_outcomes <- function(trial_data) {
  
  pain_data <- trial_data %>%
    filter(study_week %in% c(0, 24)) %>%
    group_by(patient_id) %>%
    mutate(
      baseline_pain = first(vas_pain_score),
      endpoint_pain = last(vas_pain_score),
      pain_change = endpoint_pain - baseline_pain
    ) %>%
    filter(study_week == 24) %>%
    ungroup()
  
  # ANCOVA for pain change
  pain_model <- lm(pain_change ~ treatment_arm + baseline_pain, data = pain_data)
  
  # Responder analysis (≥30% or ≥2-point reduction)
  pain_responders <- pain_data %>%
    mutate(
      pain_responder_30pct = (pain_change / baseline_pain) <= -0.3,
      pain_responder_2pt = pain_change <= -2
    ) %>%
    group_by(treatment_arm) %>%
    summarise(
      n = n(),
      mean_pain_change = mean(pain_change, na.rm = TRUE),
      prop_30pct_responder = mean(pain_responder_30pct, na.rm = TRUE),
      prop_2pt_responder = mean(pain_responder_2pt, na.rm = TRUE),
      .groups = "drop"
    )
  
  return(list(
    continuous_model = pain_model,
    responder_analysis = pain_responders
  ))
}

#' Nerve Function Analysis
#' 
#' Analyzes objective nerve function measures
#' 
#' @param trial_data Trial data
#' @return Nerve function analysis results
analyze_nerve_function <- function(trial_data) {
  
  nerve_data <- trial_data %>%
    filter(study_week %in% c(0, 24)) %>%
    group_by(patient_id) %>%
    mutate(
      baseline_ienfd = first(IENFD),
      endpoint_ienfd = last(IENFD),
      ienfd_change = endpoint_ienfd - baseline_ienfd,
      baseline_ncv = first(NCV),
      endpoint_ncv = last(NCV),
      ncv_change = endpoint_ncv - baseline_ncv
    ) %>%
    filter(study_week == 24) %>%
    ungroup()
  
  # IENFD analysis
  ienfd_model <- lm(ienfd_change ~ treatment_arm + baseline_ienfd, data = nerve_data)
  
  # NCV analysis
  ncv_model <- lm(ncv_change ~ treatment_arm + baseline_ncv, data = nerve_data)
  
  # Composite nerve function score
  nerve_composite <- nerve_data %>%
    mutate(
      nerve_composite_change = (ienfd_change / baseline_ienfd + ncv_change / baseline_ncv) / 2
    )
  
  composite_model <- lm(nerve_composite_change ~ treatment_arm, data = nerve_composite)
  
  return(list(
    ienfd_model = ienfd_model,
    ncv_model = ncv_model,
    composite_model = composite_model
  ))
}

#' Quality of Life Analysis
#' 
#' Analyzes quality of life outcomes
#' 
#' @param trial_data Trial data
#' @return QoL analysis results
analyze_quality_of_life <- function(trial_data) {
  
  qol_data <- trial_data %>%
    filter(study_week %in% c(0, 24)) %>%
    group_by(patient_id) %>%
    mutate(
      baseline_qol = first(qol_score),
      endpoint_qol = last(qol_score),
      qol_change = endpoint_qol - baseline_qol
    ) %>%
    filter(study_week == 24) %>%
    ungroup()
  
  # ANCOVA for QoL change
  qol_model <- lm(qol_change ~ treatment_arm + baseline_qol, data = qol_data)
  
  # Clinically meaningful improvement (≥5 point increase)
  qol_responders <- qol_data %>%
    mutate(qol_responder = qol_change >= 5) %>%
    group_by(treatment_arm) %>%
    summarise(
      n = n(),
      mean_qol_change = mean(qol_change, na.rm = TRUE),
      prop_qol_responder = mean(qol_responder, na.rm = TRUE),
      .groups = "drop"
    )
  
  return(list(
    continuous_model = qol_model,
    responder_analysis = qol_responders
  ))
}

#' Time-to-Event Analysis
#' 
#' Analyzes time to treatment response
#' 
#' @param trial_data Trial data
#' @return Survival analysis results
analyze_time_to_response <- function(trial_data) {
  
  # Define response criteria (30% improvement in primary endpoint)
  response_data <- trial_data %>%
    group_by(patient_id) %>%
    mutate(
      baseline_score = first(composite_neuropathy_score),
      percent_improvement = (baseline_score - composite_neuropathy_score) / baseline_score,
      response_achieved = percent_improvement >= 0.3
    ) %>%
    # Find first time of response
    filter(response_achieved) %>%
    slice_min(study_week) %>%
    select(patient_id, treatment_arm, time_to_response = study_week) %>%
    # Add patients who never responded
    right_join(
      trial_data %>% 
        select(patient_id, treatment_arm) %>% 
        distinct(),
      by = c("patient_id", "treatment_arm")
    ) %>%
    mutate(
      time_to_response = ifelse(is.na(time_to_response), 24, time_to_response),
      event = !is.na(time_to_response) & time_to_response < 24
    )
  
  # Kaplan-Meier survival curves
  surv_object <- Surv(response_data$time_to_response, response_data$event)
  km_fit <- survfit(surv_object ~ treatment_arm, data = response_data)
  
  # Cox proportional hazards model
  cox_model <- coxph(surv_object ~ treatment_arm, data = response_data)
  
  return(list(
    kaplan_meier = km_fit,
    cox_model = cox_model,
    response_data = response_data
  ))
}

#' Machine Learning Analysis
#'
#' Applies machine learning methods for predictive modeling
#'
#' @param trial_data Trial data with patient characteristics
#' @param biomarker_data Biomarker measurements
#' @return List with ML analysis results
analyze_with_machine_learning <- function(trial_data, biomarker_data) {

  # Prepare data for ML
  ml_data <- trial_data %>%
    filter(study_week == 24) %>%
    left_join(biomarker_data, by = "patient_id") %>%
    mutate(
      # Define response variable (binary)
      treatment_response = (composite_neuropathy_score - baseline_score) / baseline_score <= -0.3,
      # Weight loss response
      weight_loss_response = weight_change_percent <= -5
    )

  # Feature selection
  feature_vars <- c("age", "bmi", "diabetes_duration", "hba1c", "mnsi_score",
                   "mglur2_expression", "mglur3_expression", "sod2_activity",
                   "receptor_occupancy", "sirt1_pathway_score", "oxidative_balance")

  # Random Forest for treatment response prediction
  rf_data <- ml_data[complete.cases(ml_data[, c("treatment_response", feature_vars)]), ]

  set.seed(123)
  train_indices <- createDataPartition(rf_data$treatment_response, p = 0.7, list = FALSE)
  train_data <- rf_data[train_indices, ]
  test_data <- rf_data[-train_indices, ]

  # Random Forest model
  rf_model <- randomForest(
    as.formula(paste("treatment_response ~", paste(feature_vars, collapse = " + "))),
    data = train_data,
    ntree = 500,
    importance = TRUE
  )

  # Predictions and performance
  rf_predictions <- predict(rf_model, test_data, type = "prob")[, 2]
  rf_performance <- confusionMatrix(
    factor(rf_predictions > 0.5, levels = c(FALSE, TRUE), labels = c("No", "Yes")),
    factor(test_data$treatment_response, levels = c(FALSE, TRUE), labels = c("No", "Yes"))
  )

  # Variable importance
  rf_importance <- importance(rf_model)

  # Elastic Net for feature selection
  x_train <- model.matrix(~ . - 1, train_data[, feature_vars])
  y_train <- train_data$treatment_response

  cv_glmnet <- cv.glmnet(x_train, y_train, family = "binomial", alpha = 0.5)
  elastic_net_model <- glmnet(x_train, y_train, family = "binomial",
                             alpha = 0.5, lambda = cv_glmnet$lambda.min)

  # Support Vector Machine
  svm_model <- train(
    as.formula(paste("treatment_response ~", paste(feature_vars, collapse = " + "))),
    data = train_data,
    method = "svmRadial",
    trControl = trainControl(method = "cv", number = 5),
    tuneLength = 5
  )

  ml_results <- list(
    random_forest = list(
      model = rf_model,
      performance = rf_performance,
      importance = rf_importance,
      predictions = rf_predictions
    ),
    elastic_net = list(
      model = elastic_net_model,
      cv_results = cv_glmnet
    ),
    svm = list(
      model = svm_model
    ),
    data_split = list(
      train_data = train_data,
      test_data = test_data
    )
  )

  return(ml_results)
}

#' Bayesian Analysis
#'
#' Performs Bayesian analysis of treatment effects
#'
#' @param trial_data Trial data
#' @return Bayesian analysis results
analyze_with_bayesian_methods <- function(trial_data) {

  # Prepare data for Bayesian analysis
  bayes_data <- trial_data %>%
    filter(study_week %in% c(0, 24)) %>%
    group_by(patient_id) %>%
    mutate(
      baseline_score = first(composite_neuropathy_score),
      change_from_baseline = last(composite_neuropathy_score) - baseline_score
    ) %>%
    filter(study_week == 24) %>%
    ungroup()

  # Bayesian linear model using brms
  bayes_formula <- bf(change_from_baseline ~ treatment_arm + baseline_score +
                     diabetes_type + age + bmi)

  # Set priors (weakly informative)
  priors <- c(
    prior(normal(0, 10), class = Intercept),
    prior(normal(0, 5), class = b),
    prior(exponential(1), class = sigma)
  )

  # Fit Bayesian model
  bayes_model <- brm(
    bayes_formula,
    data = bayes_data,
    prior = priors,
    chains = 4,
    iter = 2000,
    cores = 4,
    seed = 123
  )

  # Model diagnostics
  bayes_diagnostics <- list(
    rhat = rhat(bayes_model),
    neff = neff_ratio(bayes_model),
    divergences = nuts_params(bayes_model, pars = "divergent__")
  )

  # Posterior summaries
  posterior_summary <- posterior_summary(bayes_model)

  # Probability of treatment effect
  posterior_samples <- posterior_samples(bayes_model)
  treatment_effects <- posterior_samples[, grepl("treatment_arm", names(posterior_samples))]

  # Calculate probability of clinically meaningful effect (>2.5 point improvement)
  prob_meaningful_effect <- sapply(treatment_effects, function(x) mean(x < -2.5))

  # Bayesian model comparison (if multiple models)
  # This would include WAIC, LOO-CV, etc.

  bayes_results <- list(
    model = bayes_model,
    diagnostics = bayes_diagnostics,
    posterior_summary = posterior_summary,
    probability_meaningful_effect = prob_meaningful_effect,
    posterior_samples = posterior_samples
  )

  return(bayes_results)
}

#' Biomarker Analysis
#'
#' Analyzes biomarker-outcome relationships
#'
#' @param biomarker_data Biomarker measurements
#' @param outcome_data Clinical outcomes
#' @return Biomarker analysis results
analyze_biomarker_relationships <- function(biomarker_data, outcome_data) {

  # Correlation analysis
  biomarker_numeric <- biomarker_data[, sapply(biomarker_data, is.numeric)]
  outcome_numeric <- outcome_data[, sapply(outcome_data, is.numeric)]

  correlation_matrix <- cor(biomarker_numeric, outcome_numeric, use = "complete.obs")

  # Principal component analysis
  pca_result <- prcomp(biomarker_numeric, scale. = TRUE, center = TRUE)

  # Biomarker trajectory analysis (mixed-effects models)
  trajectory_data <- biomarker_data %>%
    pivot_longer(cols = -c(patient_id, time, study_week),
                names_to = "biomarker", values_to = "value") %>%
    left_join(outcome_data %>% select(patient_id, treatment_arm), by = "patient_id")

  # Example trajectory model for one biomarker
  trajectory_model <- lmer(value ~ treatment_arm * study_week + (1 + study_week | patient_id),
                          data = trajectory_data %>% filter(biomarker == "receptor_occupancy"))

  biomarker_results <- list(
    correlations = correlation_matrix,
    pca = pca_result,
    trajectory_example = trajectory_model,
    trajectory_data = trajectory_data
  )

  return(biomarker_results)
}

#' Comprehensive Statistical Analysis
#'
#' Runs complete statistical analysis pipeline
#'
#' @param trial_data Virtual clinical trial results
#' @param biomarker_data Biomarker measurements
#' @return Complete analysis results
run_comprehensive_analysis <- function(trial_data, biomarker_data) {

  cat("Running primary endpoint analysis...\n")
  primary_analysis <- analyze_primary_endpoint(trial_data)

  cat("Running secondary endpoint analyses...\n")
  secondary_analysis <- analyze_secondary_endpoints(trial_data)

  cat("Running machine learning analysis...\n")
  ml_analysis <- analyze_with_machine_learning(trial_data, biomarker_data)

  cat("Running Bayesian analysis...\n")
  bayesian_analysis <- analyze_with_bayesian_methods(trial_data)

  cat("Running biomarker analysis...\n")
  biomarker_analysis <- analyze_biomarker_relationships(biomarker_data, trial_data)

  # Compile comprehensive results
  comprehensive_results <- list(
    primary_endpoint = primary_analysis,
    secondary_endpoints = secondary_analysis,
    machine_learning = ml_analysis,
    bayesian = bayesian_analysis,
    biomarkers = biomarker_analysis,
    analysis_timestamp = Sys.time()
  )

  cat("Statistical analysis complete!\n")
  return(comprehensive_results)
}

