{"version": "2.0.0", "tasks": [{"label": "Setup R Environment", "type": "shell", "command": "C:\\Program Files\\R\\R-4.2.2\\bin\\Rscript.exe", "args": ["-e", "source('setup.R')"], "group": "build", "problemMatcher": [], "isBackground": false}, {"label": "Run QSP Analysis", "type": "shell", "command": "C:\\Program Files\\R\\R-4.2.2\\bin\\Rscript.exe", "args": ["-e", "source('main_analysis.R')"], "group": "build", "problemMatcher": [], "isBackground": false, "dependsOn": "Setup R Environment"}, {"label": "Run Complete QSP Project", "dependsOrder": "sequence", "dependsOn": ["Setup R Environment", "Run QSP Analysis"], "group": {"kind": "build", "isDefault": true}}, {"label": "Check Project Status", "type": "shell", "command": "python", "args": ["project_helper.py"], "group": "test", "problemMatcher": [], "isBackground": false}]}