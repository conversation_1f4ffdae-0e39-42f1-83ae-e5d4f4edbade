# Data Visualization and Figure Generation
# This module generates all required figures for the QSP analysis
# including exploratory data analysis, results visualizations, and model performance metrics

library(tidyverse)
library(ggplot2)
library(plotly)
library(corrplot)
library(pheatmap)
library(gridExtra)
library(survival)
library(survminer)
library(VennDiagram)

# Set default theme
theme_set(theme_minimal() + 
          theme(plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
                axis.title = element_text(size = 12),
                axis.text = element_text(size = 10),
                legend.title = element_text(size = 11),
                legend.text = element_text(size = 10)))

#' Generate All Figures for QSP Analysis
#' 
#' Creates comprehensive set of figures for the research report
#' 
#' @param virtual_trial_results Results from virtual clinical trial
#' @param qsp_model_results QSP model simulation results
#' @param output_dir Directory to save figures
#' @return List of generated figures
generate_all_figures <- function(virtual_trial_results, qsp_model_results, output_dir = "figures/") {
  
  # Create output directories
  dir.create(file.path(output_dir, "exploratory"), recursive = TRUE, showWarnings = FALSE)
  dir.create(file.path(output_dir, "results"), recursive = TRUE, showWarnings = FALSE)
  dir.create(file.path(output_dir, "supplementary"), recursive = TRUE, showWarnings = FALSE)
  
  cat("Generating exploratory data analysis figures...\n")
  exploratory_figures <- generate_exploratory_figures(virtual_trial_results, output_dir)
  
  cat("Generating results figures...\n")
  results_figures <- generate_results_figures(virtual_trial_results, output_dir)
  
  cat("Generating model performance figures...\n")
  model_figures <- generate_model_performance_figures(qsp_model_results, output_dir)
  
  cat("Generating supplementary figures...\n")
  supplementary_figures <- generate_supplementary_figures(virtual_trial_results, qsp_model_results, output_dir)
  
  all_figures <- list(
    exploratory = exploratory_figures,
    results = results_figures,
    model_performance = model_figures,
    supplementary = supplementary_figures
  )
  
  cat("All figures generated successfully!\n")
  return(all_figures)
}

#' Generate Exploratory Data Analysis Figures
generate_exploratory_figures <- function(virtual_trial_results, output_dir) {
  
  patient_data <- virtual_trial_results$randomized_patients
  
  # Figure 1: Patient Demographics and Characteristics
  fig1a <- ggplot(patient_data, aes(x = age, fill = diabetes_type)) +
    geom_histogram(bins = 20, alpha = 0.7, position = "dodge") +
    labs(title = "Age Distribution by Diabetes Type",
         x = "Age (years)", y = "Count", fill = "Diabetes Type") +
    scale_fill_manual(values = c("Type1" = "#E74C3C", "Type2" = "#3498DB"))
  
  fig1b <- ggplot(patient_data, aes(x = diabetes_type, y = bmi, fill = diabetes_type)) +
    geom_boxplot(alpha = 0.7) +
    geom_jitter(width = 0.2, alpha = 0.3) +
    labs(title = "BMI Distribution by Diabetes Type",
         x = "Diabetes Type", y = "BMI (kg/m²)", fill = "Diabetes Type") +
    scale_fill_manual(values = c("Type1" = "#E74C3C", "Type2" = "#3498DB")) +
    guides(fill = "none")
  
  fig1c <- ggplot(patient_data, aes(x = neuropathy_severity, fill = treatment_arm)) +
    geom_bar(position = "dodge", alpha = 0.8) +
    labs(title = "Neuropathy Severity by Treatment Arm",
         x = "Neuropathy Severity", y = "Count", fill = "Treatment Arm") +
    scale_fill_brewer(type = "qual", palette = "Set2")
  
  fig1_combined <- grid.arrange(fig1a, fig1b, fig1c, ncol = 2, nrow = 2)
  ggsave(file.path(output_dir, "exploratory", "patient_demographics.png"), 
         fig1_combined, width = 12, height = 10, dpi = 300)
  
  # Figure 2: Baseline Disease Characteristics
  fig2a <- ggplot(patient_data, aes(x = hba1c, y = mnsi_score, color = diabetes_type)) +
    geom_point(alpha = 0.6, size = 2) +
    geom_smooth(method = "lm", se = TRUE) +
    labs(title = "Relationship between HbA1c and Neuropathy Severity",
         x = "HbA1c (%)", y = "MNSI Score", color = "Diabetes Type") +
    scale_color_manual(values = c("Type1" = "#E74C3C", "Type2" = "#3498DB"))
  
  fig2b <- ggplot(patient_data, aes(x = diabetes_duration, y = ienfd, color = neuropathy_severity)) +
    geom_point(alpha = 0.6, size = 2) +
    geom_smooth(method = "lm", se = TRUE) +
    labs(title = "Nerve Fiber Density vs Disease Duration",
         x = "Diabetes Duration (years)", y = "IENFD (fibers/mm)", 
         color = "Neuropathy Severity") +
    scale_color_manual(values = c("Mild" = "#2ECC71", "Moderate" = "#F39C12", "Severe" = "#E74C3C"))
  
  fig2_combined <- grid.arrange(fig2a, fig2b, ncol = 2)
  ggsave(file.path(output_dir, "exploratory", "baseline_characteristics.png"), 
         fig2_combined, width = 12, height = 6, dpi = 300)
  
  # Figure 3: Correlation Matrix of Baseline Variables
  numeric_vars <- patient_data %>%
    select(age, bmi, diabetes_duration, hba1c, mnsi_score, ienfd, ncv, 
           fasting_glucose, creatinine, egfr) %>%
    cor(use = "complete.obs")
  
  png(file.path(output_dir, "exploratory", "correlation_matrix.png"), 
      width = 10, height = 8, units = "in", res = 300)
  corrplot(numeric_vars, method = "color", type = "upper", 
           order = "hclust", tl.cex = 0.8, tl.col = "black")
  dev.off()
  
  exploratory_figs <- list(
    demographics = fig1_combined,
    baseline_characteristics = fig2_combined,
    correlation_matrix = "correlation_matrix.png"
  )
  
  return(exploratory_figs)
}

#' Generate Results Figures
generate_results_figures <- function(virtual_trial_results, output_dir) {
  
  treatment_data <- virtual_trial_results$treatment_results
  statistical_results <- virtual_trial_results$statistical_results
  
  # Figure 4: Primary Endpoint Results
  primary_summary <- statistical_results$primary_endpoint$summary_stats
  
  fig4 <- ggplot(primary_summary, aes(x = treatment_arm, y = mean_change, fill = treatment_arm)) +
    geom_col(alpha = 0.8) +
    geom_errorbar(aes(ymin = mean_change - se_change, ymax = mean_change + se_change),
                  width = 0.2) +
    labs(title = "Primary Endpoint: Change in Composite Neuropathy Score",
         subtitle = "Mean change from baseline at Week 24 (± SE)",
         x = "Treatment Arm", y = "Change from Baseline", fill = "Treatment Arm") +
    scale_fill_brewer(type = "qual", palette = "Set2") +
    guides(fill = "none") +
    geom_hline(yintercept = 0, linetype = "dashed", alpha = 0.5)
  
  ggsave(file.path(output_dir, "results", "primary_endpoint.png"), 
         fig4, width = 10, height = 6, dpi = 300)
  
  # Figure 5: Weight Loss Results
  weight_summary <- statistical_results$secondary_endpoints$weight_loss$categorical_summary
  
  fig5a <- ggplot(weight_summary, aes(x = treatment_arm, y = mean_weight_change_kg, fill = treatment_arm)) +
    geom_col(alpha = 0.8) +
    labs(title = "Weight Change by Treatment Arm",
         x = "Treatment Arm", y = "Mean Weight Change (kg)", fill = "Treatment Arm") +
    scale_fill_brewer(type = "qual", palette = "Set2") +
    guides(fill = "none")
  
  fig5b <- weight_summary %>%
    select(treatment_arm, prop_5pct_loss, prop_10pct_loss) %>%
    pivot_longer(cols = c(prop_5pct_loss, prop_10pct_loss), 
                names_to = "threshold", values_to = "proportion") %>%
    mutate(threshold = ifelse(threshold == "prop_5pct_loss", "≥5% Loss", "≥10% Loss")) %>%
    ggplot(aes(x = treatment_arm, y = proportion, fill = threshold)) +
    geom_col(position = "dodge", alpha = 0.8) +
    labs(title = "Proportion Achieving Weight Loss Thresholds",
         x = "Treatment Arm", y = "Proportion", fill = "Weight Loss") +
    scale_fill_manual(values = c("≥5% Loss" = "#3498DB", "≥10% Loss" = "#E74C3C")) +
    scale_y_continuous(labels = scales::percent)
  
  fig5_combined <- grid.arrange(fig5a, fig5b, ncol = 2)
  ggsave(file.path(output_dir, "results", "weight_loss_results.png"), 
         fig5_combined, width = 12, height = 6, dpi = 300)
  
  # Figure 6: Time Course of Treatment Effects
  time_course_data <- treatment_data %>%
    group_by(treatment_arm, study_week) %>%
    summarise(
      mean_neuropathy_score = mean(composite_neuropathy_score, na.rm = TRUE),
      se_neuropathy_score = sd(composite_neuropathy_score, na.rm = TRUE) / sqrt(n()),
      mean_weight_change = mean(weight_change_kg, na.rm = TRUE),
      se_weight_change = sd(weight_change_kg, na.rm = TRUE) / sqrt(n()),
      .groups = "drop"
    )
  
  fig6a <- ggplot(time_course_data, aes(x = study_week, y = mean_neuropathy_score, 
                                       color = treatment_arm, fill = treatment_arm)) +
    geom_line(size = 1) +
    geom_ribbon(aes(ymin = mean_neuropathy_score - se_neuropathy_score,
                    ymax = mean_neuropathy_score + se_neuropathy_score),
                alpha = 0.2) +
    labs(title = "Time Course: Neuropathy Score",
         x = "Study Week", y = "Composite Neuropathy Score",
         color = "Treatment Arm", fill = "Treatment Arm") +
    scale_color_brewer(type = "qual", palette = "Set2") +
    scale_fill_brewer(type = "qual", palette = "Set2")
  
  fig6b <- ggplot(time_course_data, aes(x = study_week, y = mean_weight_change, 
                                       color = treatment_arm, fill = treatment_arm)) +
    geom_line(size = 1) +
    geom_ribbon(aes(ymin = mean_weight_change - se_weight_change,
                    ymax = mean_weight_change + se_weight_change),
                alpha = 0.2) +
    labs(title = "Time Course: Weight Change",
         x = "Study Week", y = "Weight Change (kg)",
         color = "Treatment Arm", fill = "Treatment Arm") +
    scale_color_brewer(type = "qual", palette = "Set2") +
    scale_fill_brewer(type = "qual", palette = "Set2") +
    geom_hline(yintercept = 0, linetype = "dashed", alpha = 0.5)
  
  fig6_combined <- grid.arrange(fig6a, fig6b, ncol = 1)
  ggsave(file.path(output_dir, "results", "time_course_effects.png"), 
         fig6_combined, width = 10, height = 10, dpi = 300)
  
  results_figs <- list(
    primary_endpoint = fig4,
    weight_loss = fig5_combined,
    time_course = fig6_combined
  )
  
  return(results_figs)
}

#' Generate Model Performance Figures
generate_model_performance_figures <- function(qsp_model_results, output_dir) {
  
  # Figure 7: QSP Model Components
  if (!is.null(qsp_model_results)) {
    
    # Simulate example QSP results for visualization
    example_qsp <- simulate_integrated_qsp(dose = 40, n_doses = 168)
    
    fig7a <- ggplot(example_qsp, aes(x = time_days)) +
      geom_line(aes(y = receptor_occupancy, color = "Receptor Occupancy"), size = 1) +
      labs(title = "mGluR2/3 Receptor Occupancy Over Time",
           x = "Time (days)", y = "Receptor Occupancy (%)",
           color = "Measure") +
      scale_color_manual(values = c("Receptor Occupancy" = "#E74C3C")) +
      theme(legend.position = "none")
    
    fig7b <- ggplot(example_qsp, aes(x = time_days)) +
      geom_line(aes(y = SIRT1, color = "SIRT1"), size = 1) +
      geom_line(aes(y = PGC1a, color = "PGC-1α"), size = 1) +
      geom_line(aes(y = TFAM, color = "TFAM"), size = 1) +
      labs(title = "Neuroprotective Pathway Activation",
           x = "Time (days)", y = "Activity (% of maximum)",
           color = "Pathway Component") +
      scale_color_manual(values = c("SIRT1" = "#3498DB", "PGC-1α" = "#2ECC71", "TFAM" = "#F39C12"))
    
    fig7c <- ggplot(example_qsp, aes(x = time_days)) +
      geom_line(aes(y = Body_weight, color = "Body Weight"), size = 1) +
      labs(title = "Body Weight Change Over Time",
           x = "Time (days)", y = "Body Weight (kg)",
           color = "Measure") +
      scale_color_manual(values = c("Body Weight" = "#9B59B6")) +
      theme(legend.position = "none")
    
    fig7_combined <- grid.arrange(fig7a, fig7b, fig7c, ncol = 1)
    ggsave(file.path(output_dir, "results", "qsp_model_components.png"), 
           fig7_combined, width = 10, height = 12, dpi = 300)
    
    model_figs <- list(qsp_components = fig7_combined)
  } else {
    model_figs <- list(message = "QSP model results not available")
  }
  
  return(model_figs)
}

#' Generate Supplementary Figures
generate_supplementary_figures <- function(virtual_trial_results, qsp_model_results, output_dir) {
  
  # Supplementary Figure 1: Safety Analysis
  safety_data <- virtual_trial_results$safety_results
  
  if (!is.null(safety_data)) {
    figS1 <- ggplot(safety_data, aes(x = treatment_arm, y = ae_rate, fill = treatment_arm)) +
      geom_col(alpha = 0.8) +
      labs(title = "Adverse Event Rates by Treatment Arm",
           x = "Treatment Arm", y = "Adverse Event Rate", fill = "Treatment Arm") +
      scale_fill_brewer(type = "qual", palette = "Set2") +
      guides(fill = "none") +
      scale_y_continuous(labels = scales::percent)
    
    ggsave(file.path(output_dir, "supplementary", "safety_analysis.png"), 
           figS1, width = 8, height = 6, dpi = 300)
  }
  
  # Supplementary Figure 2: Biomarker Correlations
  biomarker_data <- virtual_trial_results$biomarker_results
  
  if (!is.null(biomarker_data)) {
    # Create heatmap of biomarker correlations
    biomarker_numeric <- biomarker_data$mechanistic[, sapply(biomarker_data$mechanistic, is.numeric)]
    biomarker_cor <- cor(biomarker_numeric, use = "complete.obs")
    
    png(file.path(output_dir, "supplementary", "biomarker_heatmap.png"), 
        width = 12, height = 10, units = "in", res = 300)
    pheatmap(biomarker_cor, 
             clustering_distance_rows = "correlation",
             clustering_distance_cols = "correlation",
             main = "Biomarker Correlation Heatmap")
    dev.off()
  }
  
  supplementary_figs <- list(
    safety = if(exists("figS1")) figS1 else "Safety data not available",
    biomarker_heatmap = "biomarker_heatmap.png"
  )
  
  return(supplementary_figs)
}
